from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_protect
from django.utils import timezone
from django.db import models
from django.db.models import Count, Sum, Avg
from django.views.decorators.csrf import csrf_exempt
from .code_execution import CodeExecutor
import json
from functools import wraps
from .models import TestVersion, ExamInstance, Player, Level, PlayerOnLevel, AppConfig, ScoringConfig
from .forms import RegistrationForm, AnswerForm
from .scoring import calculate_total_score, get_score_breakdown

# Custom decorators
def login_required_with_message(view_func):
    """Custom login_required that shows a helpful message and redirects to home"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, 'Please log in to access this page.')
            return redirect('home')
        return view_func(request, *args, **kwargs)
    return wrapper

def staff_required_with_message(view_func):
    """Custom staff_required that shows a helpful message and redirects to home"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, 'Please log in to access this page.')
            return redirect('home')
        if not request.user.is_staff:
            messages.error(request, 'Access denied. Teacher/Admin privileges required.')
            return redirect('home')
        return view_func(request, *args, **kwargs)
    return wrapper

# Create your views here.

def home_view(request):
    """Home page with login/register options"""
    if request.user.is_authenticated:
        if request.user.is_superuser:
            return redirect('/admin-panel/dashboard/')
        elif request.user.is_staff:
            return redirect('teacher_dashboard')
        elif hasattr(request.user, 'player'):
            return redirect('dashboard')
        else:
            return redirect('register')

    return render(request, 'exam/home.html')

@csrf_protect
def register_view(request):
    """Student registration with instance selection"""
    # Redirect teachers to teacher dashboard
    if request.user.is_authenticated and request.user.is_staff:
        return redirect('teacher_dashboard')

    if request.method == 'POST':
        form = RegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            instance = form.cleaned_data['instance']

            # Check if instance still allows registration
            if not instance.can_register:
                messages.error(request, f'Registration for "{instance.name}" is no longer available.')
                return render(request, 'exam/register.html', {'form': form})

            # Create player profile
            player = Player.objects.create(
                user=user,
                instance=instance
            )

            login(request, user)
            messages.success(request, f'Registration successful! You are assigned to "{instance.name}" ({instance.version.name})')
            return redirect('dashboard')
    else:
        form = RegistrationForm()

    return render(request, 'exam/register.html', {'form': form})

@csrf_protect
def login_view(request):
    """Custom login view"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)

            # Check user role and redirect accordingly
            if user.is_superuser:
                return redirect('/admin-panel/dashboard/')
            elif user.is_staff:
                return redirect('teacher_dashboard')
            elif hasattr(user, 'player'):
                return redirect('dashboard')
            else:
                return redirect('register')
        else:
            messages.error(request, 'Invalid username or password')

    return render(request, 'exam/login.html')

def logout_view(request):
    """Logout view"""
    logout(request)
    messages.success(request, 'You have been logged out successfully')
    return redirect('home')

@login_required
def dashboard_view(request):
    """Student dashboard showing simulation progress"""
    try:
        player = request.user.player
    except Player.DoesNotExist:
        messages.error(request, 'Player profile not found. Please register.')
        return redirect('register')

    # Check if simulation is paused (global or instance-specific)
    app_config = AppConfig.objects.first()
    instance_paused = player.instance.exam_paused if player.instance else False
    global_paused = app_config.exam_paused if app_config else False

    if global_paused or instance_paused:
        context = {
            'player': player,
            'instance_name': player.instance.name if player.instance else 'Unknown',
            'pause_type': 'global' if global_paused else 'instance'
        }
        return render(request, 'exam/paused.html', context)

    # Get all levels for this instance's version
    levels = Level.objects.filter(version=player.instance.version).order_by('order')

    # Get player's progress
    completed_levels = PlayerOnLevel.objects.filter(
        player=player,
        correctly_answered=True
    ).values_list('level_id', flat=True)

    # Determine current level
    if not player.start_time:
        player.start_time = timezone.now()
        player.save()

    # Set current level if not set
    if not player.current_level:
        first_level = levels.first()
        if first_level:
            player.current_level = first_level
            player.save()

    # Update current level based on progress
    if player.current_level and player.current_level.id in completed_levels:
        next_level = Level.objects.filter(
            version=player.instance.version,
            order__gt=player.current_level.order
        ).order_by('order').first()

        if next_level:
            player.current_level = next_level
            player.save()

    context = {
        'player': player,
        'levels': levels,
        'completed_levels': list(completed_levels),
        'current_level': player.current_level,
    }

    return render(request, 'exam/dashboard.html', context)

@login_required
@csrf_protect
def submit_answer(request, level_id):
    """Submit answer for a specific level"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        player = request.user.player
        level = get_object_or_404(Level, id=level_id, version=player.instance.version)

        # Check if exam is paused (global or instance-specific)
        app_config = AppConfig.objects.first()
        instance_paused = player.instance.exam_paused if player.instance else False
        global_paused = app_config.exam_paused if app_config else False

        if global_paused or instance_paused:
            return JsonResponse({'error': 'Exam is currently paused'}, status=403)

        # Validate that this is the current level
        if not can_answer_level(player, level):
            return JsonResponse({'error': 'You cannot answer this question yet'}, status=403)

        answer = request.POST.get('answer', '').strip()
        if not answer:
            return JsonResponse({'error': 'Answer cannot be empty'}, status=400)

        # Get or create PlayerOnLevel record
        player_level, created = PlayerOnLevel.objects.get_or_create(
            player=player,
            level=level,
            defaults={'start_time': timezone.now()}
        )

        # Increment attempts
        player_level.attempts += 1

        # Check if answer is correct using the level's validation method
        is_correct = level.validate_answer(answer)

        if is_correct and not player_level.correctly_answered:
            # Mark as correctly answered
            player_level.correctly_answered = True
            player_level.end_time = timezone.now()

            # Calculate time spent properly
            if player_level.start_time:
                time_spent = (player_level.end_time - player_level.start_time).total_seconds()
                player_level.time_spent_seconds = int(time_spent)
            else:
                player_level.time_spent_seconds = 0

            # Update player's current level to next level
            next_level = Level.objects.filter(
                version=player.instance.version,
                order__gt=level.order
            ).order_by('order').first()

            player.current_level = next_level

            # Calculate and update score
            update_player_score(player)

            player.save()
            player_level.save()

            # Check if simulation is complete (based on NON-BONUS questions only)
            # Get all non-bonus questions for this version
            total_non_bonus_questions = Level.objects.filter(
                version=player.instance.version,
                is_bonus=False
            ).count()

            # Get completed non-bonus questions for this player
            completed_non_bonus_questions = PlayerOnLevel.objects.filter(
                player=player,
                correctly_answered=True,
                level__is_bonus=False
            ).count()

            # Simulation is complete when all non-bonus questions are answered
            simulation_complete = completed_non_bonus_questions >= total_non_bonus_questions

            if simulation_complete:
                player.end_time = timezone.now()
                player.save()
                return JsonResponse({
                    'success': True,
                    'correct': True,
                    'message': 'Congratulations! You have completed the simulation! Bonus scenarios are still available for extra credit.',
                    'simulation_complete': True,
                    'new_score': player.score,
                    'bonus_available': Level.objects.filter(version=player.instance.version, is_bonus=True).exists()
                })

            return JsonResponse({
                'success': True,
                'correct': True,
                'message': 'Correct! Moving to next scenario.',
                'next_level_id': next_level.id if next_level else None,
                'new_score': player.score
            })
        else:
            player_level.save()
            return JsonResponse({
                'success': True,
                'correct': False,
                'message': 'Incorrect answer. Please try again.',
                'attempts': player_level.attempts
            })

    except Player.DoesNotExist:
        return JsonResponse({'error': 'Player profile not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

def can_answer_level(player, level):
    """Check if player can answer this level"""
    # First level can always be answered
    if level.order == 1:
        return True

    # Check if previous level is completed
    previous_level = Level.objects.filter(
        version=player.instance.version,
        order=level.order - 1
    ).first()

    if not previous_level:
        return False

    # Check if previous level is completed
    previous_attempt = PlayerOnLevel.objects.filter(
        player=player,
        level=previous_level,
        correctly_answered=True
    ).exists()

    return previous_attempt

def update_player_score(player):
    """Calculate and update player's total score using the scoring module"""
    player.score = calculate_total_score(player)
    player.save()  # Make sure to save the updated score
    return player.score

@login_required
def get_question(request, level_id):
    """Get question details for AJAX requests"""
    try:
        player = request.user.player
        level = get_object_or_404(Level, id=level_id, version=player.instance.version)

        # Check if this is a preview request
        is_preview = request.GET.get('preview', 'false').lower() == 'true'

        # For preview mode, allow viewing any question
        # For answer mode, check if player can answer this level
        can_answer = can_answer_level(player, level)

        if not is_preview and not can_answer:
            return JsonResponse({'error': 'You cannot answer this question yet'}, status=403)

        # Get attempt info
        player_level = PlayerOnLevel.objects.filter(
            player=player,
            level=level
        ).first()

        attempts = player_level.attempts if player_level else 0

        return JsonResponse({
            'id': level.id,
            'name': level.name,
            'description': level.description,
            'score': level.score,
            'is_bonus': level.is_bonus,
            'attempts': attempts,
            'order': level.order,
            'can_answer': can_answer,
            'is_preview': is_preview,
            'question_type': level.question_type,
            'choices': level.get_formatted_choices(),
            'explanation': level.explanation if is_preview else None
        })

    except Player.DoesNotExist:
        return JsonResponse({'error': 'Player profile not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def scoreboard_view(request):
    """Instance-specific scoreboard view"""
    # Handle teacher access
    if request.user.is_staff:
        # Get selected instance from query parameter or default to first active instance
        instance_id = request.GET.get('instance')
        if instance_id:
            try:
                current_instance = ExamInstance.objects.get(id=instance_id, is_active=True)
            except ExamInstance.DoesNotExist:
                current_instance = ExamInstance.objects.filter(is_active=True).first()
        else:
            current_instance = ExamInstance.objects.filter(is_active=True).first()

        if not current_instance:
            messages.error(request, 'No active instances found.')
            return redirect('teacher_dashboard')
    else:
        # Handle student access
        try:
            current_player = request.user.player
            current_instance = current_player.instance
        except Player.DoesNotExist:
            messages.error(request, 'Player profile not found. Please register.')
            return redirect('register')

    # Get all players in the same instance with optimized queries
    players = Player.objects.filter(instance=current_instance).select_related('user', 'instance__version')

    # Pre-fetch all player progress data to avoid N+1 queries
    player_progress = PlayerOnLevel.objects.filter(
        player__instance=current_instance
    ).select_related('player', 'level').values(
        'player_id',
        'correctly_answered',
        'attempts',
        'time_spent_seconds'
    )

    # Group progress by player
    progress_by_player = {}
    for progress in player_progress:
        player_id = progress['player_id']
        if player_id not in progress_by_player:
            progress_by_player[player_id] = {
                'completed_count': 0,
                'total_attempts': 0,
                'total_time': 0,
                'correct_time_count': 0
            }

        if progress['correctly_answered']:
            progress_by_player[player_id]['completed_count'] += 1
            progress_by_player[player_id]['total_time'] += progress['time_spent_seconds'] or 0
            progress_by_player[player_id]['correct_time_count'] += 1

        progress_by_player[player_id]['total_attempts'] += progress['attempts'] or 0

    # Get total question count once
    total_count = Level.objects.filter(version=current_instance.version).count()

    scoreboard_data = []
    total_players = players.count()
    total_score = 0
    completed_players = 0
    highest_score = 0

    for player in players:
        player_stats = progress_by_player.get(player.id, {
            'completed_count': 0,
            'total_attempts': 0,
            'total_time': 0,
            'correct_time_count': 0
        })

        completed_count = player_stats['completed_count']
        total_attempts = player_stats['total_attempts']

        # Calculate accuracy
        accuracy = (completed_count / total_attempts * 100) if total_attempts > 0 else 0

        # Calculate average time
        avg_time = (player_stats['total_time'] / player_stats['correct_time_count']) if player_stats['correct_time_count'] > 0 else 0

        player_data = {
            'username': player.user.username,
            'version': player.version.name,
            'completed_questions': completed_count,
            'total_questions': total_count,
            'score': player.score,
            'total_time': player.total_time_seconds,
            'start_time': player.start_time,
            'end_time': player.end_time,
            'accuracy': accuracy,
            'average_time': avg_time,
            'progress_percentage': (completed_count / total_count * 100) if total_count > 0 else 0
        }

        scoreboard_data.append(player_data)
        total_score += player.score
        if player.score > highest_score:
            highest_score = player.score
        if completed_count == total_count:
            completed_players += 1

    # Sort by: score (desc), questions completed (desc), time (asc)
    scoreboard_data.sort(key=lambda x: (-x['score'], -x['completed_questions'], x['total_time'] or float('inf')))

    # Calculate user rank if logged in
    user_rank = None
    user_player_data = None
    if hasattr(request.user, 'player'):
        for i, player_data in enumerate(scoreboard_data):
            if player_data['username'] == request.user.username:
                user_rank = i + 1
                user_player_data = player_data
                break

    # Get all instances for teacher dropdown
    all_instances = ExamInstance.objects.filter(is_active=True).order_by('-created_at') if request.user.is_staff else None

    context = {
        'scoreboard': scoreboard_data,
        'total_players': total_players,
        'average_score': total_score / total_players if total_players > 0 else 0,
        'completed_players': completed_players,
        'highest_score': highest_score,
        'user_rank': user_rank,
        'user_player_data': user_player_data,
        'current_instance': current_instance,
        'is_teacher': request.user.is_staff,
        'all_instances': all_instances
    }

    return render(request, 'exam/scoreboard.html', context)

@login_required
def score_breakdown_view(request):
    """Detailed score breakdown for the current player"""
    try:
        player = request.user.player
        breakdown = get_score_breakdown(player)

        context = {
            'player': player,
            'breakdown': breakdown,
        }

        return render(request, 'exam/score_breakdown.html', context)

    except Player.DoesNotExist:
        messages.error(request, 'Player profile not found.')
        return redirect('register')

@staff_required_with_message
def teacher_dashboard(request):
    """Teacher dashboard with instance management and student progress"""

    # Get selected instance or default to first active instance
    # Teachers only see their own instances, admins see all
    selected_instance_id = request.GET.get('instance')

    if request.user.is_superuser:
        # Admins see all instances
        instances = ExamInstance.objects.filter(is_active=True).order_by('-created_at')
        all_instances = ExamInstance.objects.all().order_by('-created_at')
    else:
        # Teachers only see their own instances
        instances = ExamInstance.objects.filter(
            is_active=True,
            created_by=request.user
        ).order_by('-created_at')
        all_instances = ExamInstance.objects.filter(
            created_by=request.user
        ).order_by('-created_at')

    if selected_instance_id:
        try:
            if request.user.is_superuser:
                selected_instance = ExamInstance.objects.get(id=selected_instance_id, is_active=True)
            else:
                selected_instance = ExamInstance.objects.get(
                    id=selected_instance_id,
                    is_active=True,
                    created_by=request.user
                )
        except ExamInstance.DoesNotExist:
            selected_instance = instances.first()
    else:
        selected_instance = instances.first()

    student_progress = []
    total_students = 0
    active_students = 0

    if selected_instance:
        # Get players for the selected instance with optimized queries
        players = Player.objects.filter(instance=selected_instance).select_related('user', 'instance__version')

        # Pre-fetch completion data to avoid N+1 queries
        completion_data = PlayerOnLevel.objects.filter(
            player__instance=selected_instance,
            correctly_answered=True
        ).values('player_id').annotate(
            completed_count=Count('id')
        )

        completion_by_player = {item['player_id']: item['completed_count'] for item in completion_data}

        # Get total levels count once
        total_levels = Level.objects.filter(version=selected_instance.version).count()

        for player in players:
            completed_count = completion_by_player.get(player.id, 0)

            student_progress.append({
                'player': player,
                'completed_questions': completed_count,
                'total_questions': total_levels,
                'progress_percentage': (completed_count / total_levels * 100) if total_levels > 0 else 0,
                'current_level': player.current_level.name if player.current_level else 'Not Started'
            })

        total_students = len(student_progress)
        active_students = len([p for p in student_progress if p['player'].start_time and not p['player'].end_time])

    # Get app config for exam controls
    app_config = AppConfig.objects.first()
    if not app_config:
        app_config = AppConfig.objects.create()

    # Get all active versions for the create instance modal
    all_versions = TestVersion.objects.filter(is_active=True).order_by('name')

    context = {
        'student_progress': student_progress,
        'app_config': app_config,
        'total_students': total_students,
        'active_students': active_students,
        'selected_instance': selected_instance,
        'all_instances': all_instances,
        'instances': instances,
        'all_versions': all_versions,
    }

    return render(request, 'exam/teacher_dashboard.html', context)


@staff_required_with_message
@csrf_protect
def create_instance(request):
    """Create a new exam instance"""

    if request.method == 'POST':
        try:
            name = request.POST.get('name', '').strip()
            description = request.POST.get('description', '').strip()
            version_id = request.POST.get('version_id')

            if not name:
                return JsonResponse({'error': 'Instance name is required'}, status=400)

            if not version_id:
                return JsonResponse({'error': 'Version selection is required'}, status=400)

            try:
                version = TestVersion.objects.get(id=version_id, is_active=True)
            except TestVersion.DoesNotExist:
                return JsonResponse({'error': 'Invalid version selected'}, status=400)

            # Check if instance name already exists
            if ExamInstance.objects.filter(name=name).exists():
                return JsonResponse({'error': 'Instance name already exists'}, status=400)

            # Create the instance
            instance = ExamInstance.objects.create(
                name=name,
                description=description,
                version=version,
                created_by=request.user
            )

            return JsonResponse({
                'success': True,
                'message': f'Instance "{name}" created successfully',
                'instance_id': instance.id
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@login_required
@csrf_protect
def toggle_instance_registration(request, instance_id):
    """Toggle registration for an instance"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    if request.method == 'POST':
        try:
            # Teachers can only toggle registration for their own instances
            if request.user.is_superuser:
                instance = get_object_or_404(ExamInstance, id=instance_id)
            else:
                instance = get_object_or_404(ExamInstance, id=instance_id, created_by=request.user)

            # Toggle registration
            instance.registration_open = not instance.registration_open
            instance.save()

            status = "opened" if instance.registration_open else "closed"
            return JsonResponse({
                'success': True,
                'message': f'Registration {status} for "{instance.name}"',
                'registration_open': instance.registration_open
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@login_required
@csrf_protect
def delete_instance(request, instance_id):
    """Delete an exam instance (only if exported)"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    if request.method == 'POST':
        try:
            instance = get_object_or_404(ExamInstance, id=instance_id)

            # Check if instance can be deleted (must be exported if has students)
            if not instance.can_be_deleted:
                return JsonResponse({
                    'error': f'Cannot delete instance "{instance.name}" with {instance.student_count} students. Export data first.'
                }, status=400)

            instance_name = instance.name
            student_count = instance.student_count

            # Delete the instance (this will cascade delete all associated players and their data)
            instance.delete()

            return JsonResponse({
                'success': True,
                'message': f'Instance "{instance_name}" and all {student_count} associated students deleted successfully'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@staff_required_with_message
def export_instance_csv(request, instance_id):
    """Export instance data as CSV with optional email functionality"""

    try:
        import csv
        from django.http import HttpResponse
        from django.core.mail import send_mail
        from django.template.loader import render_to_string
        from io import StringIO

        instance = get_object_or_404(ExamInstance, id=instance_id)

        # Check if this is an email request
        email_to = request.GET.get('email')

        # Create CSV content
        csv_buffer = StringIO()
        writer = csv.writer(csv_buffer)

        # Write header
        writer.writerow([
            'Username', 'Email', 'Instance', 'Version', 'Score', 'Progress',
            'Start Time', 'End Time', 'Total Time (seconds)', 'Status'
        ])

        # Write student data
        for player in instance.players.all():
            completed_count = PlayerOnLevel.objects.filter(
                player=player,
                correctly_answered=True
            ).count()

            total_count = Level.objects.filter(version=player.instance.version).count()
            progress = f"{completed_count}/{total_count}"

            status = "Completed" if player.end_time else "In Progress" if player.start_time else "Not Started"

            writer.writerow([
                player.user.username,
                player.user.email,
                instance.name,
                instance.version.name,
                player.score,
                progress,
                player.start_time.strftime('%Y-%m-%d %H:%M:%S') if player.start_time else '',
                player.end_time.strftime('%Y-%m-%d %H:%M:%S') if player.end_time else '',
                player.total_time_seconds,
                status
            ])

        # Mark instance as exported
        instance.exported_at = timezone.now()
        instance.exported_by = request.user
        instance.save()

        # If email is requested, send email with CSV attachment
        if email_to:
            try:
                app_config = AppConfig.objects.first()
                if app_config and app_config.email_host_user:

                    from django.core.mail import EmailMessage

                    # Calculate summary statistics
                    total_players = instance.players.count()
                    completed_players = instance.players.filter(end_time__isnull=False).count()
                    total_score = sum(player.score for player in instance.players.all())
                    avg_score = total_score / total_players if total_players > 0 else 0

                    subject = f'CSV Export: {instance.name} Results'

                    # Create email body with summary
                    email_body = f"""
                    <h2>Instance Export Summary</h2>
                    <p><strong>Instance:</strong> {instance.name}</p>
                    <p><strong>Version:</strong> {instance.version.name}</p>
                    <p><strong>Total Students:</strong> {total_players}</p>
                    <p><strong>Completed:</strong> {completed_players}</p>
                    <p><strong>Average Score:</strong> {avg_score:.1f}</p>
                    <p><strong>Export Time:</strong> {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

                    <p>Please find the detailed CSV export attached.</p>
                    """

                    # Create email message
                    email = EmailMessage(
                        subject=subject,
                        body=email_body,
                        from_email=app_config.email_host_user,
                        to=[email_to]
                    )
                    email.content_subtype = 'html'

                    # Attach CSV file
                    csv_content = csv_buffer.getvalue()
                    email.attach(f'{instance.name}_export.csv', csv_content, 'text/csv')

                    # Send email
                    email.send()

                    messages.success(request, f'CSV exported and emailed to {email_to}')
                    return redirect('teacher_dashboard')

                else:
                    messages.error(request, 'Email configuration not set up')
                    return redirect('teacher_dashboard')

            except Exception as e:
                messages.error(request, f'Error sending email: {str(e)}')
                return redirect('teacher_dashboard')

        # Regular CSV download
        response = HttpResponse(csv_buffer.getvalue(), content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{instance.name}_export.csv"'
        return response

    except Exception as e:
        messages.error(request, f'Export failed: {str(e)}')
        return redirect('teacher_dashboard')


@staff_required_with_message
def instance_questions(request, instance_id):
    """View questions for a specific instance (teacher access)"""

    try:
        instance = get_object_or_404(ExamInstance, id=instance_id)

        # Get all questions for this instance's version
        questions = Level.objects.filter(version=instance.version).order_by('order')

        context = {
            'instance': instance,
            'questions': questions,
            'total_questions': questions.count()
        }

        return render(request, 'exam/instance_questions.html', context)

    except Exception as e:
        messages.error(request, f'Error loading questions: {str(e)}')
        return redirect('teacher_dashboard')


@login_required
@csrf_protect
def pause_exam(request):
    """Pause/Resume exam for specific instance or all students"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    if request.method == 'POST':
        action = request.POST.get('action')
        instance_id = request.POST.get('instance_id')

        if instance_id:
            # Pause/Resume specific instance
            try:
                instance = ExamInstance.objects.get(id=instance_id)

                if action == 'pause':
                    instance.exam_paused = True
                    message = f'Exam paused for instance "{instance.name}"'
                elif action == 'resume':
                    instance.exam_paused = False
                    message = f'Exam resumed for instance "{instance.name}"'
                else:
                    return JsonResponse({'error': 'Invalid action'}, status=400)

                instance.save()
                return JsonResponse({
                    'success': True,
                    'message': message,
                    'paused': instance.exam_paused,
                    'instance_id': instance.id
                })

            except ExamInstance.DoesNotExist:
                return JsonResponse({'error': 'Instance not found'}, status=404)
        else:
            # Global pause/resume for all students
            app_config = AppConfig.objects.first()
            if not app_config:
                app_config = AppConfig.objects.create()

            if action == 'pause':
                app_config.exam_paused = True
                message = 'Exam paused for all students globally'
            elif action == 'resume':
                app_config.exam_paused = False
                message = 'Exam resumed for all students globally'
            else:
                return JsonResponse({'error': 'Invalid action'}, status=400)

            app_config.save()
            return JsonResponse({
                'success': True,
                'message': message,
                'paused': app_config.exam_paused
            })

    return JsonResponse({'error': 'Method not allowed'}, status=405)

@login_required
@csrf_protect
def reset_student_password(request, user_id):
    """Reset a student's password"""
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    if request.method == 'POST':
        try:
            user = get_object_or_404(User, id=user_id)
            new_password = request.POST.get('new_password')

            if not new_password:
                return JsonResponse({'error': 'Password cannot be empty'}, status=400)

            user.set_password(new_password)
            user.save()

            return JsonResponse({
                'success': True,
                'message': f'Password reset for {user.username}'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)

@staff_required_with_message
@csrf_protect
def end_test(request):
    """End test for specific instance"""

    if request.method == 'POST':
        try:
            instance_id = request.POST.get('instance_id')

            if instance_id:
                # End test for specific instance
                try:
                    instance = ExamInstance.objects.get(id=instance_id)

                    # Check permissions - teachers can only end their own instances
                    if not request.user.is_superuser and instance.created_by != request.user:
                        return JsonResponse({'error': 'Permission denied'}, status=403)

                    # End test for all active players in this instance
                    active_players = Player.objects.filter(
                        instance=instance,
                        start_time__isnull=False,
                        end_time__isnull=True
                    )

                    for player in active_players:
                        player.end_time = timezone.now()
                        player.save()

                    # Pause the specific instance
                    instance.exam_paused = True
                    instance.save()

                    message = f'Test ended for {active_players.count()} students in instance "{instance.name}"'

                    return JsonResponse({
                        'success': True,
                        'message': message,
                        'ended_count': active_players.count(),
                        'instance_name': instance.name
                    })

                except ExamInstance.DoesNotExist:
                    return JsonResponse({'error': 'Instance not found'}, status=404)
            else:
                return JsonResponse({'error': 'Instance ID required'}, status=400)

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@login_required
def check_pause_status(request):
    """Check if exam is paused for the current user"""
    try:
        player = request.user.player
        app_config = AppConfig.objects.first()
        instance_paused = player.instance.exam_paused if player.instance else False
        global_paused = app_config.exam_paused if app_config else False

        is_paused = global_paused or instance_paused

        return JsonResponse({
            'is_paused': is_paused,
            'pause_type': 'global' if global_paused else 'instance' if instance_paused else None,
            'instance_name': player.instance.name if player.instance else None
        })
    except Player.DoesNotExist:
        return JsonResponse({'is_paused': False})


@csrf_exempt
@login_required_with_message
def execute_code_api(request):
    """API endpoint for executing code safely"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        code = data.get('code', '')
        language = data.get('language', 'python')
        test_cases = data.get('test_cases', [])

        if not code.strip():
            return JsonResponse({
                'success': False,
                'error': 'Code cannot be empty',
                'output': '',
                'test_results': [],
                'score': 0
            })

        # Create code executor
        executor = CodeExecutor(language)

        # Validate code first
        is_valid, validation_message = executor.validate_code(code)
        if not is_valid:
            return JsonResponse({
                'success': False,
                'error': validation_message,
                'output': '',
                'test_results': [],
                'score': 0
            })

        # Execute code
        result = executor.execute_code(code, test_cases)

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data',
            'output': '',
            'test_results': [],
            'score': 0
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Execution failed: {str(e)}',
            'output': '',
            'test_results': [],
            'score': 0
        })


def handler404(request, exception):
    """Custom 404 handler - redirect to home page"""
    messages.error(request, 'Page not found. Redirecting to home page.')
    return redirect('home')


def handler403(request, exception):
    """Custom 403 handler - redirect to home page"""
    messages.error(request, 'Access denied. Redirecting to home page.')
    return redirect('home')


def handler500(request):
    """Custom 500 handler - redirect to home page"""
    messages.error(request, 'Server error occurred. Redirecting to home page.')
    return redirect('home')
