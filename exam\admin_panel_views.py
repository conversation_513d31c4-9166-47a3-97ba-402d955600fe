"""
New admin panel views with terminal-themed interface
Replaces the default Django admin with a custom KERNELiOS-themed admin panel
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import user_passes_test
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_protect
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models import Count, Q, F
from django.db import models
from functools import wraps
import csv
import io

from .models import (TestVersion, Level, Player, ExamInstance, AppConfig, ScoringConfig,
                     Announcement, EmailTemplate, ChatRoom, ChatMessage, NotificationPreference)
from .cache_utils import cache_manager, warm_cache, check_cache_health
from .analytics import analytics_engine
from .backup_system import backup_manager, schedule_backup
from .monitoring import system_monitor
from .security_audit import security_auditor

import re
from django.utils.html import escape
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from .moodle_integration import moodle_connector, moodle_grade_passback
from .maintenance_tools import maintenance_manager, automated_tester, version_manager
import json


def validate_and_sanitize_input(value, max_length=None, allow_html=False):
    """Validate and sanitize user input"""
    if not value:
        return value

    # Convert to string and strip whitespace
    value = str(value).strip()

    # Check length
    if max_length and len(value) > max_length:
        raise ValidationError(f"Input too long (max {max_length} characters)")

    # Escape HTML if not allowed
    if not allow_html:
        value = escape(value)

    return value


def validate_email_input(email):
    """Validate email input"""
    if not email:
        return email

    email = email.strip().lower()
    try:
        validate_email(email)
    except ValidationError:
        raise ValidationError("Invalid email format")

    return email


def validate_numeric_input(value, min_val=None, max_val=None):
    """Validate numeric input"""
    try:
        num_value = int(value)
        if min_val is not None and num_value < min_val:
            raise ValidationError(f"Value must be at least {min_val}")
        if max_val is not None and num_value > max_val:
            raise ValidationError(f"Value must be at most {max_val}")
        return num_value
    except (ValueError, TypeError):
        raise ValidationError("Invalid numeric value")


def admin_required(view_func):
    """Decorator to require admin/superuser access"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, 'Please log in to access this page')
            return redirect('home')
        if not request.user.is_staff:
            messages.error(request, 'Admin access required')
            return redirect('home')
        return view_func(request, *args, **kwargs)
    return wrapper

def superuser_required(view_func):
    """Decorator to require superuser access for sensitive operations"""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, 'Please log in to access this page')
            return redirect('home')
        if not request.user.is_superuser:
            messages.error(request, 'Superuser access required')
            return redirect('home')
        return view_func(request, *args, **kwargs)
    return wrapper


@admin_required
def admin_dashboard(request):
    """Main admin dashboard with system statistics and quick actions"""
    
    # Get system statistics
    stats = {
        'total_versions': TestVersion.objects.count(),
        'total_questions': Level.objects.count(),
        'text_questions': Level.objects.filter(question_type='text').count(),
        'mc_questions': Level.objects.filter(question_type='multiple_choice').count(),
        'tf_questions': Level.objects.filter(question_type='true_false').count(),
        'total_users': User.objects.count(),
        'teachers': User.objects.filter(is_staff=True).count(),
        'students': User.objects.filter(is_staff=False).count(),
        'total_instances': ExamInstance.objects.count(),
        'active_instances': ExamInstance.objects.filter(is_active=True).count(),
        'total_students': Player.objects.count(),
        'active_exams': Player.objects.filter(start_time__isnull=False, end_time__isnull=True).count(),
    }
    
    # Get app config
    app_config = AppConfig.objects.first()
    if not app_config:
        app_config = AppConfig.objects.create()
    
    # Get test versions for import modal
    test_versions = TestVersion.objects.filter(is_active=True).order_by('name')
    
    # Get recent activity (simplified for now)
    recent_activity = []
    
    # Recent test versions
    recent_versions = TestVersion.objects.order_by('-created_at')[:3]
    for version in recent_versions:
        recent_activity.append({
            'description': f'Test version "{version.name}" created',
            'timestamp': version.created_at,
            'user': None
        })
    
    # Recent exam instances
    recent_instances = ExamInstance.objects.order_by('-created_at')[:3]
    for instance in recent_instances:
        recent_activity.append({
            'description': f'Exam instance "{instance.name}" created',
            'timestamp': instance.created_at,
            'user': instance.created_by
        })
    
    # Sort by timestamp
    recent_activity.sort(key=lambda x: x['timestamp'], reverse=True)
    recent_activity = recent_activity[:10]  # Limit to 10 items
    
    context = {
        'stats': stats,
        'app_config': app_config,
        'test_versions': test_versions,
        'recent_activity': recent_activity,
    }
    
    return render(request, 'admin/admin_dashboard.html', context)


@admin_required
def admin_test_versions(request):
    """Test versions management page"""
    
    # Handle create action
    if request.GET.get('action') == 'create':
        return render(request, 'admin/test_version_create.html')
    
    # Get all test versions with statistics (fixed to avoid Django ORM Count issues)
    versions = TestVersion.objects.order_by('-created_at')

    # Add statistics manually to avoid Count aggregation issues
    for version in versions:
        version.total_questions = version.levels.count()
        version.normal_questions = version.levels.filter(is_bonus=False).count()
        version.bonus_questions = version.levels.filter(is_bonus=True).count()
        version.total_instances = version.instances.count()
    
    context = {
        'versions': versions,
    }
    
    return render(request, 'admin/test_versions.html', context)


@admin_required
def admin_test_version_detail(request, version_id):
    """Detailed view of a specific test version with question management"""
    
    version = get_object_or_404(TestVersion, id=version_id)
    questions = version.levels.order_by('order')
    
    # Get score distribution
    score_distribution = version.get_score_distribution()
    
    context = {
        'version': version,
        'questions': questions,
        'score_distribution': score_distribution,
    }
    
    return render(request, 'admin/test_version_detail.html', context)


@admin_required
def admin_create_test_version(request):
    """Create a new test version with optional immediate question creation"""

    if request.method == 'POST':
        action = request.POST.get('action', 'create_version')

        if action == 'create_version':
            # Create the test version
            name = request.POST.get('name', '').strip()
            description = request.POST.get('description', '').strip()
            setup_type = request.POST.get('setup_type', 'empty')

            if not name:
                messages.error(request, 'Version name is required')
                return render(request, 'admin/test_version_create.html')

            # Check if name already exists
            if TestVersion.objects.filter(name=name).exists():
                messages.error(request, f'Version "{name}" already exists')
                return render(request, 'admin/test_version_create.html')

            try:
                version = TestVersion.objects.create(
                    name=name,
                    description=description
                )

                # Handle different setup types
                if setup_type == 'template':
                    # Add template questions
                    _create_template_questions(version)
                elif setup_type == 'import':
                    # Handle CSV import
                    csv_file = request.FILES.get('csv_file')
                    if csv_file:
                        _import_questions_from_csv(version, csv_file)

                messages.success(request, f'Test version "{name}" created successfully')

                # If empty setup, redirect to the same page with question builder
                if setup_type == 'empty':
                    return redirect('admin_panel:admin_create_test_version_with_questions', version_id=version.id)
                else:
                    return redirect('admin_panel:admin_test_version_detail', version_id=version.id)

            except Exception as e:
                messages.error(request, f'Error creating version: {str(e)}')

        elif action == 'add_question':
            # Handle question creation for existing version
            version_id = request.POST.get('version_id')
            version = get_object_or_404(TestVersion, id=version_id)

            # Use the same question creation logic from admin_create_question
            try:
                _create_question_from_post(request, version)
                messages.success(request, 'Question added successfully')
            except Exception as e:
                messages.error(request, f'Error adding question: {str(e)}')

            return redirect('admin_panel:admin_create_test_version_with_questions', version_id=version.id)

    return render(request, 'admin/test_version_create.html')


def _create_template_questions(version):
    """Create template questions for a new version"""
    template_questions = [
        {
            'name': 'Basic Security Question',
            'description': 'What is the default SSH port?',
            'question_type': 'text',
            'correct_answer': '22',
            'order': 1,
            'score': 5,
            'is_bonus': False
        },
        {
            'name': 'Network Protocol',
            'description': 'Which protocol is used for secure web browsing?',
            'question_type': 'multiple_choice',
            'correct_answer': 'A',
            'choices': {'A': 'HTTPS', 'B': 'HTTP', 'C': 'FTP', 'D': 'SMTP'},
            'order': 2,
            'score': 5,
            'is_bonus': False
        }
    ]

    for q_data in template_questions:
        Level.objects.create(version=version, **q_data)

    # Recalculate scores
    version.calculate_automatic_scores()


def _import_questions_from_csv(version, csv_file):
    """Import questions from CSV file"""
    try:
        file_data = csv_file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(file_data))

        for row_num, row in enumerate(csv_reader, start=1):
            question_type = row.get('question_type', 'text').lower()
            if question_type not in ['text', 'multiple_choice', 'true_false']:
                question_type = 'text'

            choices = None
            if question_type == 'multiple_choice' and 'choices' in row:
                try:
                    choice_pairs = row['choices'].split('|')
                    choices = {}
                    for pair in choice_pairs:
                        if ':' in pair:
                            key, value = pair.split(':', 1)
                            choices[key.strip().upper()] = value.strip()
                except:
                    choices = None

            Level.objects.create(
                name=row.get('Title', f'Question {row_num}'),
                description=row.get('Description', ''),
                correct_answer=row.get('Answer', ''),
                score=int(row.get('points', 5)),
                is_bonus=row.get('is_bonus', '').upper() == 'TRUE',
                question_type=question_type,
                choices=choices,
                version=version,
                order=row_num
            )

        version.calculate_automatic_scores()

    except Exception as e:
        raise Exception(f'Error importing CSV: {str(e)}')


def _create_question_from_post(request, version):
    """Create a question from POST data with comprehensive validation"""
    name = request.POST.get('name', '').strip()
    description = request.POST.get('description', '').strip()
    question_type = request.POST.get('question_type', 'text')

    # Validate and set order
    try:
        order = int(request.POST.get('order', version.levels.count() + 1))
        if order < 1:
            order = version.levels.count() + 1
    except (ValueError, TypeError):
        order = version.levels.count() + 1

    # Validate and set score
    try:
        score = int(request.POST.get('score', 5))
        if score < 1 or score > 100:
            score = 5
    except (ValueError, TypeError):
        score = 5

    is_bonus = request.POST.get('is_bonus') == 'true'
    explanation = request.POST.get('explanation', '').strip()

    # Validate required fields
    if not name or not description:
        raise Exception('Question title and description are required')

    if len(name) > 200:
        raise Exception('Question title must be 200 characters or less')

    # Validate question type
    if question_type not in ['text', 'multiple_choice', 'true_false']:
        question_type = 'text'

    # Handle different question types
    correct_answer = ''
    choices = None

    if question_type == 'text':
        correct_answer = request.POST.get('correct_answer_text', '').strip()
        if not correct_answer:
            raise Exception('Correct answer is required for text questions')

    elif question_type == 'multiple_choice':
        correct_choice = request.POST.get('correct_choice', '')
        choice_a = request.POST.get('choice_a', '').strip()
        choice_b = request.POST.get('choice_b', '').strip()
        choice_c = request.POST.get('choice_c', '').strip()
        choice_d = request.POST.get('choice_d', '').strip()

        if not all([correct_choice, choice_a, choice_b, choice_c, choice_d]):
            raise Exception('All choices and correct answer selection are required')

        correct_answer = correct_choice
        choices = {'A': choice_a, 'B': choice_b, 'C': choice_c, 'D': choice_d}

    elif question_type == 'true_false':
        correct_tf = request.POST.get('correct_tf', '')
        if not correct_tf:
            raise Exception('Correct answer is required for true/false questions')

        correct_answer = correct_tf
        choices = {'A': 'True', 'B': 'False'}

    # Check if order already exists and adjust if necessary
    existing_question = Level.objects.filter(version=version, order=order).first()
    if existing_question:
        Level.objects.filter(version=version, order__gte=order).update(order=F('order') + 1)

    # Create the question
    Level.objects.create(
        name=name,
        description=description,
        question_type=question_type,
        correct_answer=correct_answer,
        choices=choices,
        order=order,
        score=score,
        is_bonus=is_bonus,
        explanation=explanation,
        version=version
    )

    # Recalculate scores automatically
    version.calculate_automatic_scores()


@admin_required
def admin_create_test_version_with_questions(request, version_id):
    """Create test version page with integrated question builder"""

    version = get_object_or_404(TestVersion, id=version_id)
    questions = version.levels.order_by('order')
    score_distribution = version.get_score_distribution()

    context = {
        'version': version,
        'questions': questions,
        'score_distribution': score_distribution,
        'is_creation_mode': True,
    }

    return render(request, 'admin/test_version_create_with_questions.html', context)


@superuser_required
def admin_users(request):
    """User management page"""

    # Get all users with statistics
    users = User.objects.annotate(
        player_count=Count('player', distinct=True)
    ).order_by('-date_joined')

    context = {
        'users': users,
    }

    return render(request, 'admin/users.html', context)


@admin_required
def admin_instances(request):
    """Exam instances management page - admin sees all instances"""

    # Get all instances with statistics (admins see everything)
    instances = ExamInstance.objects.annotate(
        total_students=Count('players', distinct=True),
        active_students=Count('players', filter=Q(players__start_time__isnull=False, players__end_time__isnull=True), distinct=True),
        completed_students=Count('players', filter=Q(players__end_time__isnull=False), distinct=True)
    ).select_related('version', 'created_by').order_by('-created_at')

    # Get statistics
    stats = {
        'total_instances': instances.count(),
        'active_instances': instances.filter(is_active=True).count(),
        'open_registration': instances.filter(registration_open=True).count(),
        'total_students': sum(instance.total_students for instance in instances),
    }

    context = {
        'instances': instances,
        'stats': stats,
    }

    return render(request, 'admin/instances.html', context)


@admin_required
def admin_create_user(request):
    """Create a new user"""

    if request.method == 'POST':
        try:
            username = request.POST.get('username', '').strip()
            email = request.POST.get('email', '').strip()
            password = request.POST.get('password', '')
            password_confirm = request.POST.get('password_confirm', '')
            role = request.POST.get('role', 'student')

            # Validation
            if not username or not password:
                messages.error(request, 'Username and password are required')
                return redirect('admin_users')

            if password != password_confirm:
                messages.error(request, 'Passwords do not match')
                return redirect('admin_users')

            if len(password) < 6:
                messages.error(request, 'Password must be at least 6 characters long')
                return redirect('admin_users')

            if User.objects.filter(username=username).exists():
                messages.error(request, f'Username "{username}" already exists')
                return redirect('admin_users')

            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password
            )

            # Set role
            if role == 'admin':
                user.is_staff = True
                user.is_superuser = True
                user.save()
            elif role == 'teacher':
                user.is_staff = True
                user.save()
            # student role doesn't need any special permissions

            messages.success(request, f'User "{username}" created successfully as {role}')

        except Exception as e:
            messages.error(request, f'Error creating user: {str(e)}')

    return redirect('admin_users')


@admin_required
def admin_edit_user(request):
    """Edit an existing user"""

    if request.method == 'POST':
        try:
            user_id = request.POST.get('user_id')
            user = get_object_or_404(User, id=user_id)

            # Update user fields
            user.username = request.POST.get('username', '').strip()
            user.email = request.POST.get('email', '').strip()
            user.first_name = request.POST.get('first_name', '').strip()
            user.last_name = request.POST.get('last_name', '').strip()
            user.is_active = request.POST.get('is_active') == 'on'

            # Handle role assignment
            role = request.POST.get('role', 'student')
            if role == 'admin':
                user.is_staff = True
                user.is_superuser = True
            elif role == 'teacher':
                user.is_staff = True
                user.is_superuser = False
            else:  # student
                user.is_staff = False
                user.is_superuser = False

            # Update password if provided
            new_password = request.POST.get('password', '').strip()
            if new_password:
                if len(new_password) < 6:
                    messages.error(request, 'Password must be at least 6 characters long')
                    return redirect('admin_panel:admin_users')
                user.set_password(new_password)

            # Validation
            if not user.username:
                messages.error(request, 'Username is required')
                return redirect('admin_panel:admin_users')

            if not user.email:
                messages.error(request, 'Email is required')
                return redirect('admin_panel:admin_users')

            # Check for duplicate username (excluding current user)
            if User.objects.filter(username=user.username).exclude(id=user.id).exists():
                messages.error(request, f'Username "{user.username}" already exists')
                return redirect('admin_panel:admin_users')

            user.save()
            messages.success(request, f'User "{user.username}" updated successfully')

        except Exception as e:
            messages.error(request, f'Error updating user: {str(e)}')

    return redirect('admin_panel:admin_users')


@admin_required
def admin_users_api(request):
    """API endpoint for user operations"""

    if request.method == 'GET':
        action = request.GET.get('action')

        if action == 'get_user':
            user_id = request.GET.get('user_id')
            try:
                user = get_object_or_404(User, id=user_id)
                return JsonResponse({
                    'success': True,
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'email': user.email,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'is_active': user.is_active,
                        'is_staff': user.is_staff,
                        'is_superuser': user.is_superuser,
                        'date_joined': user.date_joined.isoformat(),
                        'last_login': user.last_login.isoformat() if user.last_login else None,
                    }
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': str(e)
                })

        return JsonResponse({'success': False, 'message': 'Invalid action'})

    return JsonResponse({'success': False, 'message': 'Invalid method'})


@admin_required
def admin_delete_user(request, user_id):
    """Delete a user"""

    user = get_object_or_404(User, id=user_id)

    # Prevent deletion of superusers and self
    if user.is_superuser:
        messages.error(request, 'Cannot delete superuser accounts')
        return redirect('admin_users')

    if user == request.user:
        messages.error(request, 'Cannot delete your own account')
        return redirect('admin_users')

    if request.method == 'POST':
        try:
            username = user.username
            user.delete()
            messages.success(request, f'User "{username}" deleted successfully')

        except Exception as e:
            messages.error(request, f'Error deleting user: {str(e)}')

    return redirect('admin_users')


@superuser_required
@csrf_protect
def admin_settings(request):
    """System settings page"""
    
    app_config = AppConfig.objects.first()
    if not app_config:
        app_config = AppConfig.objects.create()
    
    scoring_config = ScoringConfig.objects.first()
    if not scoring_config:
        scoring_config = ScoringConfig.objects.create()
    
    if request.method == 'POST':
        # Handle settings update
        try:
            # Update app config with validation
            app_config.email_host = request.POST.get('email_host', '').strip()

            # Validate and set email port
            try:
                email_port = int(request.POST.get('email_port', 587))
                if email_port < 1 or email_port > 65535:
                    raise ValueError("Port must be between 1 and 65535")
                app_config.email_port = email_port
            except (ValueError, TypeError):
                messages.error(request, 'Invalid email port. Using default 587.')
                app_config.email_port = 587

            app_config.email_use_tls = request.POST.get('email_use_tls') == 'on'
            app_config.email_host_user = request.POST.get('email_host_user', '').strip()
            app_config.email_host_password = request.POST.get('email_host_password', '').strip()
            app_config.require_email_verification = request.POST.get('require_email_verification') == 'on'
            app_config.save()

            # Update scoring config with validation
            try:
                scoring_config.incorrect_attempts_penalized_from = max(1, int(request.POST.get('incorrect_attempts_penalized_from', 2)))
            except (ValueError, TypeError):
                scoring_config.incorrect_attempts_penalized_from = 2

            try:
                scoring_config.attempt_penalty_per_mistake = max(0.0, float(request.POST.get('attempt_penalty_per_mistake', 0.5)))
            except (ValueError, TypeError):
                scoring_config.attempt_penalty_per_mistake = 0.5

            try:
                scoring_config.max_attempt_penalty_per_question = max(0.0, float(request.POST.get('max_attempt_penalty_per_question', 1.5)))
            except (ValueError, TypeError):
                scoring_config.max_attempt_penalty_per_question = 1.5

            try:
                scoring_config.time_penalty_threshold_minutes = max(1, int(request.POST.get('time_penalty_threshold_minutes', 5)))
            except (ValueError, TypeError):
                scoring_config.time_penalty_threshold_minutes = 5

            try:
                scoring_config.time_penalty_per_minute = max(0.0, float(request.POST.get('time_penalty_per_minute', 0.25)))
            except (ValueError, TypeError):
                scoring_config.time_penalty_per_minute = 0.25

            try:
                scoring_config.max_time_penalty_per_question = max(0.0, float(request.POST.get('max_time_penalty_per_question', 1.5)))
            except (ValueError, TypeError):
                scoring_config.max_time_penalty_per_question = 1.5

            scoring_config.save()

            messages.success(request, 'Settings updated successfully')

        except Exception as e:
            messages.error(request, f'Error updating settings: {str(e)}')
    
    context = {
        'app_config': app_config,
        'scoring_config': scoring_config,
    }
    
    return render(request, 'admin/settings.html', context)


@superuser_required
@csrf_protect
def admin_test_email(request):
    """Test email configuration"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        import json
        from django.core.mail import send_mail
        from django.conf import settings

        data = json.loads(request.body)

        # Temporarily override email settings
        original_settings = {}
        email_settings = {
            'EMAIL_HOST': data.get('email_host', ''),
            'EMAIL_PORT': int(data.get('email_port', 587)),
            'EMAIL_USE_TLS': data.get('email_use_tls', True),
            'EMAIL_HOST_USER': data.get('email_host_user', ''),
            'EMAIL_HOST_PASSWORD': data.get('email_host_password', ''),
        }

        # Backup original settings
        for key in email_settings:
            original_settings[key] = getattr(settings, key, None)
            setattr(settings, key, email_settings[key])

        try:
            # Send test email
            send_mail(
                subject='KERNELiOS Email Test',
                message='This is a test email from KERNELiOS Secure Exam System. If you receive this, your email configuration is working correctly.',
                from_email=email_settings['EMAIL_HOST_USER'],
                recipient_list=[email_settings['EMAIL_HOST_USER']],  # Send to self
                fail_silently=False
            )

            return JsonResponse({'success': True, 'message': 'Test email sent successfully'})

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

        finally:
            # Restore original settings
            for key, value in original_settings.items():
                setattr(settings, key, value)

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@superuser_required
def admin_cache_management(request):
    """Cache management interface"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'clear_all':
            success = cache_manager.flush_all()
            if success:
                messages.success(request, 'All cache cleared successfully')
            else:
                messages.error(request, 'Failed to clear cache')

        elif action == 'warm_cache':
            success = warm_cache()
            if success:
                messages.success(request, 'Cache warmed successfully')
            else:
                messages.error(request, 'Failed to warm cache')

        elif action == 'clear_prefix':
            prefix = request.POST.get('prefix')
            if prefix:
                success = cache_manager.clear_prefix(prefix)
                if success:
                    messages.success(request, f'Cache prefix "{prefix}" cleared successfully')
                else:
                    messages.error(request, f'Failed to clear cache prefix "{prefix}"')
            else:
                messages.error(request, 'No prefix specified')

        return redirect('admin_panel:admin_cache_management')

    # Get cache statistics
    cache_stats = cache_manager.get_stats()
    cache_health, health_message = check_cache_health()

    # Get cache prefixes
    cache_prefixes = list(cache_manager.PREFIXES.keys())

    context = {
        'cache_stats': cache_stats,
        'cache_health': cache_health,
        'health_message': health_message,
        'cache_prefixes': cache_prefixes,
    }

    return render(request, 'admin/cache_management.html', context)


@superuser_required
def admin_cache_api(request):
    """API endpoint for cache operations"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        action = data.get('action')

        if action == 'get_stats':
            stats = cache_manager.get_stats()
            health, message = check_cache_health()

            return JsonResponse({
                'success': True,
                'stats': stats,
                'health': health,
                'health_message': message
            })

        elif action == 'clear_all':
            success = cache_manager.flush_all()
            return JsonResponse({
                'success': success,
                'message': 'Cache cleared successfully' if success else 'Failed to clear cache'
            })

        elif action == 'warm_cache':
            success = warm_cache()
            return JsonResponse({
                'success': success,
                'message': 'Cache warmed successfully' if success else 'Failed to warm cache'
            })

        elif action == 'clear_prefix':
            prefix = data.get('prefix')
            if prefix:
                success = cache_manager.clear_prefix(prefix)
                return JsonResponse({
                    'success': success,
                    'message': f'Cache prefix "{prefix}" cleared' if success else f'Failed to clear prefix "{prefix}"'
                })
            else:
                return JsonResponse({'success': False, 'message': 'No prefix specified'})

        else:
            return JsonResponse({'success': False, 'message': 'Invalid action'})

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Invalid JSON data'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@admin_required
def admin_communications(request):
    """Communication management dashboard"""

    # Get recent announcements
    announcements = Announcement.objects.filter(is_active=True).order_by('-created_at')[:10]

    # Get email templates
    email_templates = EmailTemplate.objects.filter(is_active=True).order_by('template_type')

    # Get active chat rooms
    chat_rooms = ChatRoom.objects.filter(is_active=True).order_by('-created_at')[:5]

    # Get communication statistics
    stats = {
        'total_announcements': Announcement.objects.count(),
        'active_announcements': Announcement.objects.filter(is_active=True).count(),
        'email_templates': EmailTemplate.objects.filter(is_active=True).count(),
        'active_chat_rooms': ChatRoom.objects.filter(is_active=True).count(),
        'total_messages': ChatMessage.objects.count(),
    }

    context = {
        'announcements': announcements,
        'email_templates': email_templates,
        'chat_rooms': chat_rooms,
        'stats': stats,
    }

    return render(request, 'admin/communications.html', context)


@admin_required
def admin_create_announcement(request):
    """Create new announcement"""

    if request.method == 'POST':
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()
        announcement_type = request.POST.get('announcement_type', 'info')
        is_active = request.POST.get('is_active') == 'on'

        if not title or not content:
            messages.error(request, 'Title and content are required')
            return redirect('admin_panel:admin_communications')

        try:
            announcement = Announcement.objects.create(
                title=title,
                content=content,
                announcement_type=announcement_type,
                is_active=is_active,
                created_by=request.user
            )

            messages.success(request, f'Announcement "{title}" created successfully')
            return redirect('admin_panel:admin_communications')

        except Exception as e:
            messages.error(request, f'Error creating announcement: {str(e)}')
            return redirect('admin_panel:admin_communications')

    return redirect('admin_panel:admin_communications')


@admin_required
def admin_announcements(request):
    """Announcement management"""

    if request.method == 'POST':
        try:
            title = request.POST.get('title', '').strip()
            message = request.POST.get('message', '').strip()
            priority = request.POST.get('priority', 'normal')
            audience = request.POST.get('audience', 'all')
            target_instance_id = request.POST.get('target_instance')
            show_popup = request.POST.get('show_popup') == 'on'
            auto_dismiss = request.POST.get('auto_dismiss') == 'on'
            dismiss_after_seconds = int(request.POST.get('dismiss_after_seconds', 10))
            expires_at = request.POST.get('expires_at')

            if not title or not message:
                messages.error(request, 'Title and message are required')
                return redirect('admin_panel:admin_announcements')

            # Handle target instance
            target_instance = None
            if audience == 'instance' and target_instance_id:
                try:
                    target_instance = ExamInstance.objects.get(id=target_instance_id)
                except ExamInstance.DoesNotExist:
                    messages.error(request, 'Invalid target instance')
                    return redirect('admin_panel:admin_announcements')

            # Handle expiration date
            expires_at_datetime = None
            if expires_at:
                from datetime import datetime
                try:
                    expires_at_datetime = datetime.fromisoformat(expires_at.replace('T', ' '))
                except ValueError:
                    messages.error(request, 'Invalid expiration date format')
                    return redirect('admin_panel:admin_announcements')

            # Create announcement
            announcement = Announcement.objects.create(
                title=title,
                message=message,
                priority=priority,
                audience=audience,
                target_instance=target_instance,
                show_popup=show_popup,
                auto_dismiss=auto_dismiss,
                dismiss_after_seconds=dismiss_after_seconds,
                expires_at=expires_at_datetime,
                created_by=request.user
            )

            messages.success(request, f'Announcement "{title}" created successfully')

        except Exception as e:
            messages.error(request, f'Error creating announcement: {str(e)}')

        return redirect('admin_panel:admin_announcements')

    # Get all announcements
    announcements = Announcement.objects.all().order_by('-created_at')

    # Get active instances for targeting
    active_instances = ExamInstance.objects.filter(is_active=True).order_by('name')

    context = {
        'announcements': announcements,
        'active_instances': active_instances,
    }

    return render(request, 'admin/announcements.html', context)


@admin_required
def admin_email_templates(request):
    """Email template management"""

    if request.method == 'POST':
        try:
            name = request.POST.get('name', '').strip()
            template_type = request.POST.get('template_type', 'custom')
            subject = request.POST.get('subject', '').strip()
            html_content = request.POST.get('html_content', '').strip()
            text_content = request.POST.get('text_content', '').strip()

            if not name or not subject or not html_content:
                messages.error(request, 'Name, subject, and HTML content are required')
                return redirect('admin_panel:admin_email_templates')

            # Create email template
            template = EmailTemplate.objects.create(
                name=name,
                template_type=template_type,
                subject=subject,
                html_content=html_content,
                text_content=text_content,
                created_by=request.user
            )

            messages.success(request, f'Email template "{name}" created successfully')

        except Exception as e:
            messages.error(request, f'Error creating email template: {str(e)}')

        return redirect('admin_panel:admin_email_templates')

    # Get all email templates
    templates = EmailTemplate.objects.all().order_by('template_type', 'name')

    context = {
        'templates': templates,
        'template_types': EmailTemplate.TEMPLATE_TYPES,
    }

    return render(request, 'admin/email_templates.html', context)


@admin_required
def admin_chat_management(request):
    """Chat room management"""

    if request.method == 'POST':
        try:
            name = request.POST.get('name', '').strip()
            room_type = request.POST.get('room_type', 'instance')
            instance_id = request.POST.get('instance_id')
            allow_students = request.POST.get('allow_students') == 'on'
            allow_teachers = request.POST.get('allow_teachers') == 'on'

            if not name:
                messages.error(request, 'Room name is required')
                return redirect('admin_panel:admin_chat_management')

            # Handle instance for instance chat rooms
            instance = None
            if room_type == 'instance' and instance_id:
                try:
                    instance = ExamInstance.objects.get(id=instance_id)
                except ExamInstance.DoesNotExist:
                    messages.error(request, 'Invalid instance selected')
                    return redirect('admin_panel:admin_chat_management')

            # Create chat room
            chat_room = ChatRoom.objects.create(
                name=name,
                room_type=room_type,
                instance=instance,
                allow_students=allow_students,
                allow_teachers=allow_teachers,
                created_by=request.user
            )

            messages.success(request, f'Chat room "{name}" created successfully')

        except Exception as e:
            messages.error(request, f'Error creating chat room: {str(e)}')

        return redirect('admin_panel:admin_chat_management')

    # Get all chat rooms
    chat_rooms = ChatRoom.objects.all().order_by('-created_at')

    # Get active instances
    active_instances = ExamInstance.objects.filter(is_active=True).order_by('name')

    # Get recent messages for each room
    for room in chat_rooms:
        room.recent_messages = room.messages.order_by('-created_at')[:5]
        room.message_count = room.messages.count()

    context = {
        'chat_rooms': chat_rooms,
        'active_instances': active_instances,
        'room_types': ChatRoom.ROOM_TYPES,
    }

    return render(request, 'admin/chat_management.html', context)


@admin_required
def admin_analytics_dashboard(request):
    """Advanced analytics dashboard"""

    # Get selected instance if any
    selected_instance_id = request.GET.get('instance_id')
    selected_instance = None

    if selected_instance_id:
        try:
            selected_instance = ExamInstance.objects.get(id=selected_instance_id)
        except ExamInstance.DoesNotExist:
            selected_instance_id = None

    # Get real-time metrics
    real_time_metrics = analytics_engine.get_real_time_metrics(selected_instance_id)

    # Get performance analytics
    performance_analytics = analytics_engine.get_performance_analytics(selected_instance_id)

    # Get trend analysis
    trend_days = int(request.GET.get('trend_days', 7))
    trend_analysis = analytics_engine.get_trend_analysis(trend_days)

    # Get system health metrics
    system_health = analytics_engine.get_system_health_metrics()

    # Get user engagement metrics
    engagement_metrics = analytics_engine.get_user_engagement_metrics()

    # Get available instances for filtering
    available_instances = ExamInstance.objects.filter(is_active=True).order_by('name')

    context = {
        'real_time_metrics': real_time_metrics,
        'performance_analytics': performance_analytics,
        'trend_analysis': trend_analysis,
        'system_health': system_health,
        'engagement_metrics': engagement_metrics,
        'available_instances': available_instances,
        'selected_instance': selected_instance,
        'selected_instance_id': selected_instance_id,
        'trend_days': trend_days,
    }

    return render(request, 'admin/analytics_dashboard.html', context)


@admin_required
def admin_analytics_api(request):
    """API endpoint for analytics data"""

    if request.method != 'GET':
        return JsonResponse({'error': 'GET method required'}, status=405)

    try:
        data_type = request.GET.get('type', 'real_time')
        instance_id = request.GET.get('instance_id')

        if data_type == 'real_time':
            data = analytics_engine.get_real_time_metrics(instance_id)
        elif data_type == 'performance':
            data = analytics_engine.get_performance_analytics(instance_id)
        elif data_type == 'trends':
            days = int(request.GET.get('days', 7))
            data = analytics_engine.get_trend_analysis(days)
        elif data_type == 'system_health':
            data = analytics_engine.get_system_health_metrics()
        elif data_type == 'engagement':
            data = analytics_engine.get_user_engagement_metrics()
        else:
            return JsonResponse({'error': 'Invalid data type'}, status=400)

        return JsonResponse({
            'success': True,
            'data': data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@superuser_required
def admin_backup_management(request):
    """Backup management interface"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'create_backup':
            include_media = request.POST.get('include_media') == 'on'
            compress = request.POST.get('compress') == 'on'
            encrypt = request.POST.get('encrypt') == 'on'

            result = backup_manager.create_full_backup(
                include_media=include_media,
                compress=compress,
                encrypt=encrypt
            )

            if result['success']:
                messages.success(request, f'Backup created successfully: {result["backup_name"]}')
            else:
                messages.error(request, f'Backup failed: {result.get("error", "Unknown error")}')

        elif action == 'delete_backup':
            backup_name = request.POST.get('backup_name')
            if backup_name:
                success = backup_manager.delete_backup(backup_name)
                if success:
                    messages.success(request, f'Backup "{backup_name}" deleted successfully')
                else:
                    messages.error(request, f'Failed to delete backup "{backup_name}"')
            else:
                messages.error(request, 'No backup specified for deletion')

        elif action == 'cleanup_old':
            keep_days = int(request.POST.get('keep_days', 30))
            keep_count = int(request.POST.get('keep_count', 10))

            deleted_count = backup_manager.cleanup_old_backups(keep_days, keep_count)
            messages.success(request, f'Cleanup completed: {deleted_count} old backups deleted')

        return redirect('admin_panel:admin_backup_management')

    # Get backup list
    backups = backup_manager.list_backups()

    # Calculate total backup size
    total_size = 0
    for backup in backups:
        try:
            size_str = backup['size'].split()[0]
            size_val = float(size_str)
            unit = backup['size'].split()[1]

            if unit == 'KB':
                total_size += size_val * 1024
            elif unit == 'MB':
                total_size += size_val * 1024 * 1024
            elif unit == 'GB':
                total_size += size_val * 1024 * 1024 * 1024
            else:
                total_size += size_val
        except:
            pass

    # Format total size
    if total_size < 1024:
        total_size_str = f"{total_size:.1f} B"
    elif total_size < 1024 * 1024:
        total_size_str = f"{total_size / 1024:.1f} KB"
    elif total_size < 1024 * 1024 * 1024:
        total_size_str = f"{total_size / (1024 * 1024):.1f} MB"
    else:
        total_size_str = f"{total_size / (1024 * 1024 * 1024):.1f} GB"

    context = {
        'backups': backups,
        'backup_count': len(backups),
        'total_size': total_size_str,
        'backup_dir': backup_manager.backup_dir,
    }

    return render(request, 'admin/backup_management.html', context)


@superuser_required
def admin_backup_api(request):
    """API endpoint for backup operations"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        action = data.get('action')

        if action == 'create_backup':
            include_media = data.get('include_media', True)
            compress = data.get('compress', True)
            encrypt = data.get('encrypt', True)

            result = backup_manager.create_full_backup(
                include_media=include_media,
                compress=compress,
                encrypt=encrypt
            )

            return JsonResponse(result)

        elif action == 'list_backups':
            backups = backup_manager.list_backups()
            return JsonResponse({
                'success': True,
                'backups': backups
            })

        elif action == 'delete_backup':
            backup_name = data.get('backup_name')
            if backup_name:
                success = backup_manager.delete_backup(backup_name)
                return JsonResponse({
                    'success': success,
                    'message': f'Backup deleted' if success else 'Failed to delete backup'
                })
            else:
                return JsonResponse({'success': False, 'message': 'No backup specified'})

        elif action == 'cleanup_old':
            keep_days = data.get('keep_days', 30)
            keep_count = data.get('keep_count', 10)

            deleted_count = backup_manager.cleanup_old_backups(keep_days, keep_count)
            return JsonResponse({
                'success': True,
                'deleted_count': deleted_count,
                'message': f'{deleted_count} old backups deleted'
            })

        elif action == 'schedule_backup':
            result = schedule_backup()
            return JsonResponse(result)

        else:
            return JsonResponse({'success': False, 'message': 'Invalid action'})

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Invalid JSON data'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@admin_required
def admin_monitoring_dashboard(request):
    """System monitoring dashboard"""

    # Get current system health
    health_status = system_monitor.check_health_status()

    # Get performance summary
    performance_summary = system_monitor.get_performance_summary()

    # Get recent performance metrics
    system_metrics = system_monitor.get_system_metrics()
    db_metrics = system_monitor.get_database_metrics()
    app_metrics = system_monitor.get_application_metrics()
    cache_metrics = system_monitor.get_cache_metrics()

    context = {
        'health_status': health_status,
        'performance_summary': performance_summary,
        'system_metrics': system_metrics,
        'database_metrics': db_metrics,
        'application_metrics': app_metrics,
        'cache_metrics': cache_metrics,
    }

    return render(request, 'admin/monitoring_dashboard.html', context)


@admin_required
def admin_monitoring_api(request):
    """API endpoint for monitoring data"""

    if request.method != 'GET':
        return JsonResponse({'error': 'GET method required'}, status=405)

    try:
        data_type = request.GET.get('type', 'health')

        if data_type == 'health':
            data = system_monitor.check_health_status()
        elif data_type == 'system':
            data = system_monitor.get_system_metrics()
        elif data_type == 'database':
            data = system_monitor.get_database_metrics()
        elif data_type == 'application':
            data = system_monitor.get_application_metrics()
        elif data_type == 'cache':
            data = system_monitor.get_cache_metrics()
        elif data_type == 'performance':
            data = system_monitor.get_performance_summary()
        elif data_type == 'reset_counters':
            system_monitor.reset_counters()
            data = {'message': 'Performance counters reset successfully'}
        else:
            return JsonResponse({'error': 'Invalid data type'}, status=400)

        return JsonResponse({
            'success': True,
            'data': data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@superuser_required
def admin_security_audit(request):
    """Security audit dashboard"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'run_audit':
            audit_results = security_auditor.run_full_audit()

            # Store audit results in session for display
            request.session['last_audit_results'] = audit_results
            messages.success(request, 'Security audit completed successfully')

        return redirect('admin_panel:admin_security_audit')

    # Get last audit results from session
    audit_results = request.session.get('last_audit_results')

    context = {
        'audit_results': audit_results,
        'has_results': audit_results is not None,
    }

    return render(request, 'admin/security_audit.html', context)


@superuser_required
def admin_security_api(request):
    """API endpoint for security operations"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        action = data.get('action')

        if action == 'run_audit':
            audit_results = security_auditor.run_full_audit()
            return JsonResponse({
                'success': True,
                'audit_results': audit_results
            })

        else:
            return JsonResponse({'success': False, 'message': 'Invalid action'})

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Invalid JSON data'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@admin_required
def admin_moodle_integration(request):
    """Moodle LMS integration management"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'test_connection':
            result = moodle_connector.test_connection()
            if result.get('success'):
                messages.success(request, f'Connected to {result["site_name"]} (Moodle {result["moodle_version"]})')
            else:
                messages.error(request, f'Connection failed: {result.get("error", "Unknown error")}')

        elif action == 'sync_course_users':
            course_id = request.POST.get('course_id')
            if course_id:
                result = moodle_connector.bulk_sync_course_users(int(course_id))
                if 'error' not in result:
                    messages.success(request, f'Synced {result["created"]} new users, updated {result["updated"]} existing users')
                else:
                    messages.error(request, f'Sync failed: {result["error"]}')
            else:
                messages.error(request, 'No course selected')

        elif action == 'send_grades':
            instance_id = request.POST.get('instance_id')
            course_id = request.POST.get('course_id')
            grade_item_id = request.POST.get('grade_item_id')

            if instance_id and course_id and grade_item_id:
                result = moodle_grade_passback.bulk_send_instance_grades(
                    int(instance_id), int(course_id), int(grade_item_id)
                )
                if 'error' not in result:
                    messages.success(request, f'Sent {result["successful"]} grades successfully')
                    if result["failed"] > 0:
                        messages.warning(request, f'{result["failed"]} grades failed to send')
                else:
                    messages.error(request, f'Grade passback failed: {result["error"]}')
            else:
                messages.error(request, 'Missing required parameters for grade passback')

        return redirect('admin_panel:admin_moodle_integration')

    # Get Moodle connection status
    connection_status = moodle_connector.test_connection()

    # Get available courses if connected
    courses = []
    if connection_status.get('success'):
        courses_result = moodle_connector.get_courses()
        if 'courses' in courses_result:
            courses = courses_result['courses']

    # Get available exam instances
    exam_instances = ExamInstance.objects.filter(is_active=True).order_by('name')

    context = {
        'connection_status': connection_status,
        'courses': courses,
        'exam_instances': exam_instances,
        'moodle_configured': bool(moodle_connector.moodle_url and moodle_connector.token),
    }

    return render(request, 'admin/moodle_integration.html', context)


@admin_required
def admin_moodle_api(request):
    """API endpoint for Moodle operations"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        action = data.get('action')

        if action == 'test_connection':
            result = moodle_connector.test_connection()
            return JsonResponse(result)

        elif action == 'get_courses':
            result = moodle_connector.get_courses()
            return JsonResponse(result)

        elif action == 'get_course_users':
            course_id = data.get('course_id')
            if course_id:
                result = moodle_connector.get_course_users(course_id)
                return JsonResponse(result)
            else:
                return JsonResponse({'error': 'Course ID required'})

        elif action == 'sync_user':
            user_id = data.get('user_id')
            if user_id:
                result = moodle_connector.sync_user_from_moodle(user_id)
                return JsonResponse(result)
            else:
                return JsonResponse({'error': 'User ID required'})

        elif action == 'send_grade':
            course_id = data.get('course_id')
            user_id = data.get('user_id')
            grade_item = data.get('grade_item')
            grade_value = data.get('grade_value')
            max_grade = data.get('max_grade', 100)

            if all([course_id, user_id, grade_item, grade_value is not None]):
                result = moodle_connector.send_grade(
                    course_id, user_id, grade_item, grade_value, max_grade
                )
                return JsonResponse(result)
            else:
                return JsonResponse({'error': 'Missing required parameters'})

        else:
            return JsonResponse({'error': 'Invalid action'})

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'})
    except Exception as e:
        return JsonResponse({'error': str(e)})


@superuser_required
def admin_maintenance_tools(request):
    """Maintenance and monitoring tools dashboard"""

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'enable_maintenance':
            message = request.POST.get('message', 'System is under maintenance. Please try again later.')
            maintenance_manager.enable_maintenance_mode(message)
            messages.success(request, 'Maintenance mode enabled')

        elif action == 'disable_maintenance':
            maintenance_manager.disable_maintenance_mode()
            messages.success(request, 'Maintenance mode disabled')

        elif action == 'run_health_tests':
            test_results = automated_tester.run_health_tests()
            request.session['last_test_results'] = test_results
            messages.success(request, f'Health tests completed: {test_results["passed"]} passed, {test_results["failed"]} failed')

        elif action == 'set_version':
            version = request.POST.get('version', '').strip()
            if version:
                if version_manager.set_version(version):
                    messages.success(request, f'Version set to: {version}')
                else:
                    messages.error(request, 'Failed to set version')
            else:
                messages.error(request, 'Version cannot be empty')

        return redirect('admin_panel:admin_maintenance_tools')

    # Get system status
    system_status = maintenance_manager.get_system_status()

    # Get version information
    current_version = version_manager.get_current_version()
    git_info = version_manager.get_git_info()

    # Get last test results
    test_results = request.session.get('last_test_results')

    context = {
        'system_status': system_status,
        'current_version': current_version,
        'git_info': git_info,
        'test_results': test_results,
        'maintenance_mode': maintenance_manager.is_maintenance_mode(),
        'maintenance_message': maintenance_manager.get_maintenance_message(),
    }

    return render(request, 'admin/maintenance_tools.html', context)


@superuser_required
def admin_maintenance_api(request):
    """API endpoint for maintenance operations"""

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    try:
        data = json.loads(request.body)
        action = data.get('action')

        if action == 'get_system_status':
            status = maintenance_manager.get_system_status()
            return JsonResponse({
                'success': True,
                'status': status
            })

        elif action == 'enable_maintenance':
            message = data.get('message', 'System is under maintenance.')
            maintenance_manager.enable_maintenance_mode(message)
            return JsonResponse({
                'success': True,
                'message': 'Maintenance mode enabled'
            })

        elif action == 'disable_maintenance':
            maintenance_manager.disable_maintenance_mode()
            return JsonResponse({
                'success': True,
                'message': 'Maintenance mode disabled'
            })

        elif action == 'run_health_tests':
            test_results = automated_tester.run_health_tests()
            return JsonResponse({
                'success': True,
                'test_results': test_results
            })

        elif action == 'get_version_info':
            version_info = {
                'current_version': version_manager.get_current_version(),
                'git_info': version_manager.get_git_info()
            }
            return JsonResponse({
                'success': True,
                'version_info': version_info
            })

        elif action == 'set_version':
            version = data.get('version', '').strip()
            if version:
                success = version_manager.set_version(version)
                return JsonResponse({
                    'success': success,
                    'message': f'Version {"set" if success else "failed to set"} to: {version}'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'Version cannot be empty'
                })

        else:
            return JsonResponse({'success': False, 'message': 'Invalid action'})

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Invalid JSON data'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@admin_required
def admin_test_version_wizard(request):
    """Enhanced test version creation wizard"""

    if request.method == 'POST':
        try:
            # Extract form data
            name = request.POST.get('name', '').strip()
            description = request.POST.get('description', '').strip()
            subject = request.POST.get('subject', '')
            difficulty = request.POST.get('difficulty', 'intermediate')
            template = request.POST.get('template', 'blank')

            # Question configuration
            total_questions = int(request.POST.get('total_questions', 10))
            bonus_questions = int(request.POST.get('bonus_questions', 0))
            max_score = int(request.POST.get('max_score', 100))
            passing_score = int(request.POST.get('passing_score', 70))

            # Question types
            question_types = request.POST.getlist('question_types')

            # Advanced settings
            enable_time_limit = request.POST.get('enable_time_limit') == 'on'
            time_limit = int(request.POST.get('time_limit', 60)) if enable_time_limit else None
            randomize_questions = request.POST.get('randomize_questions') == 'on'
            allow_review = request.POST.get('allow_review') == 'on'
            show_results = request.POST.get('show_results') == 'on'
            prevent_cheating = request.POST.get('prevent_cheating') == 'on'
            lockdown_mode = request.POST.get('lockdown_mode') == 'on'

            # Creation options
            create_instance = request.POST.get('create_instance') == 'on'
            open_editor = request.POST.get('open_editor') == 'on'

            # Validate required fields
            if not name:
                messages.error(request, 'Test name is required')
                return redirect('admin_panel:admin_test_version_wizard')

            # Create test version
            test_version = TestVersion.objects.create(
                name=name,
                description=description,
                max_score=max_score,
                created_by=request.user
            )

            # Create questions based on template and configuration
            questions_created = create_questions_from_template(
                test_version, template, total_questions, bonus_questions,
                question_types, max_score
            )

            # Store advanced settings in test version (if model supports it)
            # This would require extending the TestVersion model

            messages.success(request, f'Test version "{name}" created successfully with {questions_created} questions')

            # Handle post-creation actions
            if create_instance:
                # Create an exam instance
                instance = ExamInstance.objects.create(
                    name=f"{name} - Instance",
                    version=test_version,
                    created_by=request.user,
                    is_active=True,
                    open_for_registration=False
                )
                messages.success(request, f'Exam instance "{instance.name}" created')

            if open_editor:
                # Redirect to question editor
                return redirect('admin_panel:admin_edit_test_version', version_id=test_version.id)
            else:
                # Redirect to test versions list
                return redirect('admin_panel:admin_test_versions')

        except ValueError as e:
            messages.error(request, f'Invalid input: {str(e)}')
            return redirect('admin_panel:admin_test_version_wizard')
        except Exception as e:
            messages.error(request, f'Error creating test version: {str(e)}')
            return redirect('admin_panel:admin_test_version_wizard')

    return render(request, 'admin/test_version_wizard.html')


def create_questions_from_template(test_version, template, total_questions, bonus_questions, question_types, max_score):
    """Create questions based on template and configuration"""

    questions_created = 0

    try:
        # Calculate score per question
        regular_questions = total_questions
        score_per_question = max_score // regular_questions if regular_questions > 0 else 0
        bonus_score_per_question = (max_score * 0.2) // bonus_questions if bonus_questions > 0 else 0

        # Distribute questions among types
        if question_types:
            questions_per_type = regular_questions // len(question_types)
            remainder = regular_questions % len(question_types)

            question_number = 1

            for i, question_type in enumerate(question_types):
                type_count = questions_per_type + (1 if i < remainder else 0)

                for j in range(type_count):
                    question_data = get_template_question_data(template, question_type, question_number)

                    level = Level.objects.create(
                        version=test_version,
                        level_number=question_number,
                        question=question_data['question'],
                        correct_answer=question_data['correct_answer'],
                        score=score_per_question,
                        question_type=question_type,
                        choices=question_data.get('choices', ''),
                        code_template=question_data.get('code_template', ''),
                        expected_output=question_data.get('expected_output', ''),
                        drag_drop_items=question_data.get('drag_drop_items', ''),
                        is_bonus=False
                    )

                    questions_created += 1
                    question_number += 1

            # Create bonus questions
            for i in range(bonus_questions):
                bonus_type = question_types[i % len(question_types)]
                question_data = get_template_question_data(template, bonus_type, question_number, is_bonus=True)

                level = Level.objects.create(
                    version=test_version,
                    level_number=question_number,
                    question=question_data['question'],
                    correct_answer=question_data['correct_answer'],
                    score=int(bonus_score_per_question),
                    question_type=bonus_type,
                    choices=question_data.get('choices', ''),
                    code_template=question_data.get('code_template', ''),
                    expected_output=question_data.get('expected_output', ''),
                    drag_drop_items=question_data.get('drag_drop_items', ''),
                    is_bonus=True
                )

                questions_created += 1
                question_number += 1

        return questions_created

    except Exception as e:
        logger.error(f"Error creating questions from template: {str(e)}")
        return questions_created


def get_template_question_data(template, question_type, question_number, is_bonus=False):
    """Get question data based on template and type"""

    bonus_prefix = "BONUS: " if is_bonus else ""

    templates = {
        'programming': {
            'text': {
                'question': f"{bonus_prefix}What is the output of the following Python code?\n\nprint('Hello, World!')",
                'correct_answer': 'Hello, World!'
            },
            'multiple_choice': {
                'question': f"{bonus_prefix}Which of the following is a Python data type?",
                'correct_answer': 'list',
                'choices': 'int\nfloat\nstring\nlist'
            },
            'code': {
                'question': f"{bonus_prefix}Write a function that returns the sum of two numbers:",
                'correct_answer': 'def add(a, b):\n    return a + b',
                'code_template': 'def add(a, b):\n    # Your code here\n    pass',
                'expected_output': '5'
            }
        },
        'multiple_choice': {
            'multiple_choice': {
                'question': f"{bonus_prefix}Sample multiple choice question {question_number}",
                'correct_answer': 'Option A',
                'choices': 'Option A\nOption B\nOption C\nOption D'
            }
        },
        'mixed': {
            'text': {
                'question': f"{bonus_prefix}Sample text question {question_number}",
                'correct_answer': 'Sample answer'
            },
            'multiple_choice': {
                'question': f"{bonus_prefix}Sample multiple choice question {question_number}",
                'correct_answer': 'Option A',
                'choices': 'Option A\nOption B\nOption C\nOption D'
            },
            'drag_drop': {
                'question': f"{bonus_prefix}Match the following items:",
                'correct_answer': 'Item 1:Match 1,Item 2:Match 2',
                'drag_drop_items': 'Item 1,Item 2|Match 1,Match 2'
            }
        }
    }

    # Default template
    default_data = {
        'text': {
            'question': f"{bonus_prefix}Sample question {question_number}",
            'correct_answer': 'Sample answer'
        },
        'multiple_choice': {
            'question': f"{bonus_prefix}Sample multiple choice question {question_number}",
            'correct_answer': 'Option A',
            'choices': 'Option A\nOption B\nOption C\nOption D'
        },
        'code': {
            'question': f"{bonus_prefix}Write a simple function:",
            'correct_answer': 'def sample():\n    return True',
            'code_template': 'def sample():\n    # Your code here\n    pass',
            'expected_output': 'True'
        },
        'drag_drop': {
            'question': f"{bonus_prefix}Match the items:",
            'correct_answer': 'Item 1:Match 1',
            'drag_drop_items': 'Item 1|Match 1'
        }
    }

    # Get template-specific data or fall back to default
    template_data = templates.get(template, {})
    question_data = template_data.get(question_type, default_data.get(question_type, default_data['text']))

    return question_data


def _parse_json_field(json_string):
    """Helper function to safely parse JSON fields"""
    if not json_string or json_string.strip() == '':
        return None

    try:
        return json.loads(json_string)
    except json.JSONDecodeError:
        return None


@admin_required
def admin_export_test_version_csv(request, version_id):
    """Export test version questions to CSV"""

    version = get_object_or_404(TestVersion, id=version_id)

    # Create the HttpResponse object with CSV header
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{version.name}_questions.csv"'

    writer = csv.writer(response)

    # Write header row
    writer.writerow([
        'Title',
        'Description',
        'question_type',
        'Answer',
        'choices',
        'points',
        'is_bonus',
        'order',
        'explanation'
    ])

    # Write question data
    for question in version.levels.order_by('order'):
        choices_str = ''
        if question.choices:
            # Format choices as "A:Option A|B:Option B|C:Option C|D:Option D"
            choices_str = '|'.join([f"{k}:{v}" for k, v in question.choices.items()])

        writer.writerow([
            question.name,
            question.description,
            question.question_type,
            question.correct_answer,
            choices_str,
            question.score,
            question.is_bonus,
            question.order,
            question.explanation or ''
        ])

    return response


@admin_required
def admin_import_test_version_csv(request, version_id):
    """Import questions from CSV to test version"""

    version = get_object_or_404(TestVersion, id=version_id)

    if request.method == 'POST':
        csv_file = request.FILES.get('csv_file')

        if not csv_file:
            messages.error(request, 'Please select a CSV file to import')
            return redirect('admin_panel:admin_test_version_detail', version_id=version.id)

        if not csv_file.name.lower().endswith('.csv'):
            messages.error(request, 'Please upload a valid CSV file')
            return redirect('admin_panel:admin_test_version_detail', version_id=version.id)

        # Check file size (limit to 5MB)
        if csv_file.size > 5 * 1024 * 1024:
            messages.error(request, 'File too large. Maximum size is 5MB')
            return redirect('admin_panel:admin_test_version_detail', version_id=version.id)

        try:
            # Read and parse CSV with encoding detection
            file_content = csv_file.read()

            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']
            file_data = None

            for encoding in encodings:
                try:
                    file_data = file_content.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue

            if file_data is None:
                messages.error(request, 'Unable to decode CSV file. Please ensure it uses UTF-8 encoding')
                return redirect('admin_panel:admin_test_version_detail', version_id=version.id)

            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(file_data))

            # Validate CSV headers
            required_headers = ['Title', 'Description', 'Answer']
            if not all(header in csv_reader.fieldnames for header in required_headers):
                missing_headers = [h for h in required_headers if h not in csv_reader.fieldnames]
                messages.error(request, f'Missing required CSV headers: {", ".join(missing_headers)}')
                return redirect('admin_panel:admin_test_version_detail', version_id=version.id)

            imported_count = 0
            errors = []

            for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 because row 1 is header
                try:
                    # Validate required fields
                    title = row.get('Title', '').strip()
                    description = row.get('Description', '').strip()
                    answer = row.get('Answer', '').strip()

                    if not title:
                        errors.append(f"Row {row_num}: Title is required")
                        continue

                    if not description:
                        errors.append(f"Row {row_num}: Description is required")
                        continue

                    if not answer:
                        errors.append(f"Row {row_num}: Answer is required")
                        continue

                    # Parse question type
                    question_type = row.get('question_type', 'text').lower().strip()
                    if question_type not in ['text', 'multiple_choice', 'true_false']:
                        question_type = 'text'

                    # Parse choices for multiple choice questions
                    choices = None
                    if question_type == 'multiple_choice':
                        choices_str = row.get('choices', '').strip()
                        if choices_str:
                            try:
                                choice_pairs = choices_str.split('|')
                                choices = {}
                                for pair in choice_pairs:
                                    if ':' in pair:
                                        key, value = pair.split(':', 1)
                                        choices[key.strip().upper()] = value.strip()

                                # Validate we have A, B, C, D
                                if not all(k in choices for k in ['A', 'B', 'C', 'D']):
                                    errors.append(f"Row {row_num}: Multiple choice questions must have choices A, B, C, D")
                                    continue

                            except Exception:
                                errors.append(f"Row {row_num}: Invalid choices format. Use 'A:Option A|B:Option B|C:Option C|D:Option D'")
                                continue
                        else:
                            errors.append(f"Row {row_num}: Multiple choice questions require choices")
                            continue

                    elif question_type == 'true_false':
                        choices = {'A': 'True', 'B': 'False'}
                        # Validate answer is A or B
                        if answer.upper() not in ['A', 'B', 'TRUE', 'FALSE']:
                            errors.append(f"Row {row_num}: True/False questions must have answer 'A' (True) or 'B' (False)")
                            continue
                        # Convert TRUE/FALSE to A/B
                        if answer.upper() == 'TRUE':
                            answer = 'A'
                        elif answer.upper() == 'FALSE':
                            answer = 'B'

                    # Parse numeric fields
                    try:
                        points = int(row.get('points', 5))
                        if points < 1 or points > 100:
                            points = 5
                    except (ValueError, TypeError):
                        points = 5

                    try:
                        order = int(row.get('order', version.levels.count() + 1))
                    except (ValueError, TypeError):
                        order = version.levels.count() + 1

                    # Parse boolean fields
                    is_bonus = str(row.get('is_bonus', 'false')).lower() in ['true', '1', 'yes', 'y']

                    # Get explanation
                    explanation = row.get('explanation', '').strip()

                    # Check if order already exists and adjust
                    existing_question = Level.objects.filter(version=version, order=order).first()
                    if existing_question:
                        # Find next available order
                        max_order = version.levels.aggregate(models.Max('order'))['order__max'] or 0
                        order = max_order + 1

                    # Create the question
                    Level.objects.create(
                        name=title,
                        description=description,
                        question_type=question_type,
                        correct_answer=answer,
                        choices=choices,
                        order=order,
                        score=points,
                        is_bonus=is_bonus,
                        explanation=explanation,
                        version=version
                    )

                    imported_count += 1

                except Exception as e:
                    errors.append(f"Row {row_num}: {str(e)}")

            # Recalculate scores after import
            if imported_count > 0:
                version.calculate_automatic_scores()

            # Show results
            if imported_count > 0:
                messages.success(request, f'Successfully imported {imported_count} questions')

            if errors:
                error_msg = f"Import completed with {len(errors)} errors:\n" + "\n".join(errors[:10])
                if len(errors) > 10:
                    error_msg += f"\n... and {len(errors) - 10} more errors"
                messages.warning(request, error_msg)

            if imported_count == 0 and errors:
                messages.error(request, 'No questions were imported due to errors')

        except Exception as e:
            messages.error(request, f'Error processing CSV file: {str(e)}')

    return redirect('admin_panel:admin_test_version_detail', version_id=version.id)


@admin_required
def admin_download_csv_template(request):
    """Download CSV template for importing questions or bulk scenarios"""

    template_type = request.GET.get('type', 'questions')

    if template_type == 'bulk':
        # Bulk question import template (same as regular but with more examples)
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="bulk_questions_import_template.csv"'

        writer = csv.writer(response)

        # Write header row
        writer.writerow([
            'Title',
            'Description',
            'question_type',
            'Answer',
            'choices',
            'points',
            'is_bonus',
            'order',
            'explanation'
        ])

        # Write comprehensive example rows for bulk import
        writer.writerow([
            'Basic Port Knowledge',
            'What is the default SSH port number?',
            'text',
            '22',
            '',
            '5',
            'false',
            '1',
            'SSH (Secure Shell) uses port 22 by default for secure remote access'
        ])

        writer.writerow([
            'Web Security Protocol',
            'Which protocol is used for secure web browsing?',
            'multiple_choice',
            'A',
            'A:HTTPS|B:HTTP|C:FTP|D:SMTP',
            '5',
            'false',
            '2',
            'HTTPS provides encryption and authentication for web traffic'
        ])

        writer.writerow([
            'Authentication Security',
            'Is two-factor authentication more secure than single-factor authentication?',
            'true_false',
            'A',
            'A:True|B:False',
            '3',
            'false',
            '3',
            'Two-factor authentication adds an extra layer of security beyond just passwords'
        ])

        writer.writerow([
            'Advanced Network Challenge',
            'What is the maximum number of TCP ports available?',
            'text',
            '65535',
            '',
            '10',
            'true',
            '4',
            'TCP ports range from 0 to 65535 (2^16 - 1), providing 65536 total ports'
        ])

        writer.writerow([
            'Firewall Concepts',
            'Which type of firewall operates at the application layer?',
            'multiple_choice',
            'C',
            'A:Packet Filter|B:Stateful|C:Application Gateway|D:Circuit Level',
            '7',
            'false',
            '5',
            'Application gateways (proxy firewalls) operate at Layer 7 of the OSI model'
        ])

        return response

    else:
        # Regular question import template
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="question_import_template.csv"'

        writer = csv.writer(response)

        # Write header row
        writer.writerow([
            'Title',
            'Description',
            'question_type',
            'Answer',
            'choices',
            'points',
            'is_bonus',
            'order',
            'explanation'
        ])

        # Write example rows
        writer.writerow([
            'Port Scanning Basics',
            'What is the default SSH port number?',
            'text',
            '22',
            '',
            '5',
            'false',
            '1',
            'SSH (Secure Shell) uses port 22 by default'
        ])

    writer.writerow([
        'Network Protocol Question',
        'Which protocol is used for secure web browsing?',
        'multiple_choice',
        'A',
        'A:HTTPS|B:HTTP|C:FTP|D:SMTP',
        '5',
        'false',
        '2',
        'HTTPS provides encryption for web traffic'
    ])

    writer.writerow([
        'Security Concept',
        'Is two-factor authentication more secure than single-factor?',
        'true_false',
        'A',
        '',
        '3',
        'false',
        '3',
        'Two-factor authentication adds an extra layer of security'
    ])

    writer.writerow([
        'Bonus Challenge',
        'What is the maximum number of TCP ports?',
        'text',
        '65535',
        '',
        '10',
        'true',
        '4',
        'TCP ports range from 0 to 65535 (2^16 - 1)'
    ])

    return response


@admin_required
def admin_import_questions(request):
    """Import questions from CSV file"""
    
    if request.method == 'POST':
        csv_file = request.FILES.get('csv_file')
        version_id = request.POST.get('version_id')
        
        if not csv_file or not version_id:
            messages.error(request, 'Please provide both CSV file and test version.')
            return redirect('admin_dashboard')
        
        try:
            version = TestVersion.objects.get(id=version_id)
            
            # Read CSV file
            file_data = csv_file.read().decode('utf-8')
            csv_reader = csv.DictReader(io.StringIO(file_data))
            
            imported_count = 0
            for row_num, row in enumerate(csv_reader, start=2):
                try:
                    # Get question type (default to text for backward compatibility)
                    question_type = row.get('question_type', 'text').lower()
                    if question_type not in ['text', 'multiple_choice', 'true_false']:
                        question_type = 'text'
                    
                    # Parse choices for multiple choice questions
                    choices = None
                    if question_type == 'multiple_choice' and 'choices' in row:
                        try:
                            # Expected format: "A:Option 1|B:Option 2|C:Option 3|D:Option 4"
                            choice_pairs = row['choices'].split('|')
                            choices = {}
                            for pair in choice_pairs:
                                if ':' in pair:
                                    key, value = pair.split(':', 1)
                                    choices[key.strip().upper()] = value.strip()
                        except:
                            choices = None
                    
                    # Create level from CSV row
                    Level.objects.create(
                        name=row['Title'],
                        description=row['Description'],
                        correct_answer=row['Answer'],
                        score=int(row.get('points', 5)),
                        is_bonus=row.get('is_bonus', '').upper() == 'TRUE',
                        question_type=question_type,
                        choices=choices,
                        version=version,
                        order=row_num - 1  # Start from 1
                    )
                    imported_count += 1
                except Exception as e:
                    messages.warning(request, f'Error importing row {row_num}: {str(e)}')
            
            # Recalculate scores automatically
            version.calculate_automatic_scores()
            
            messages.success(request, f'Successfully imported {imported_count} questions to {version.name}')
            
        except Exception as e:
            messages.error(request, f'Error importing questions: {str(e)}')
    
    return redirect('admin_dashboard')


@admin_required
def admin_create_question(request, version_id):
    """Create a new question for a test version"""

    version = get_object_or_404(TestVersion, id=version_id)

    if request.method == 'POST':
        try:
            # Get form data
            name = request.POST.get('name', '').strip()
            description = request.POST.get('description', '').strip()
            question_type = request.POST.get('question_type', 'text')
            order = int(request.POST.get('order', 1))
            score = int(request.POST.get('score', 5))
            is_bonus = request.POST.get('is_bonus') == 'true'
            explanation = request.POST.get('explanation', '').strip()

            # Validate required fields
            if not name or not description:
                messages.error(request, 'Question title and description are required')
                return redirect('admin_test_version_detail', version_id=version_id)

            # Handle different question types
            correct_answer = ''
            choices = None

            if question_type == 'text':
                correct_answer = request.POST.get('correct_answer_text', '').strip()
                if not correct_answer:
                    messages.error(request, 'Correct answer is required for text questions')
                    return redirect('admin_test_version_detail', version_id=version_id)

            elif question_type == 'multiple_choice':
                correct_choice = request.POST.get('correct_choice', '')
                choice_a = request.POST.get('choice_a', '').strip()
                choice_b = request.POST.get('choice_b', '').strip()
                choice_c = request.POST.get('choice_c', '').strip()
                choice_d = request.POST.get('choice_d', '').strip()

                if not all([correct_choice, choice_a, choice_b, choice_c, choice_d]):
                    messages.error(request, 'All choices and correct answer selection are required for multiple choice questions')
                    return redirect('admin_test_version_detail', version_id=version_id)

                correct_answer = correct_choice
                choices = {
                    'A': choice_a,
                    'B': choice_b,
                    'C': choice_c,
                    'D': choice_d
                }

            elif question_type == 'true_false':
                correct_tf = request.POST.get('correct_tf', '')
                if not correct_tf:
                    messages.error(request, 'Correct answer is required for true/false questions')
                    return redirect('admin_test_version_detail', version_id=version_id)

                correct_answer = correct_tf
                choices = {'A': 'True', 'B': 'False'}

            elif question_type == 'code_execution':
                # For code execution, we don't need a traditional correct answer
                correct_answer = 'AUTO_GRADED'  # Placeholder

            elif question_type == 'drag_drop':
                # For drag and drop, we don't need a traditional correct answer
                correct_answer = 'DRAG_DROP_MATCH'  # Placeholder

            # Check if order already exists and adjust if necessary
            existing_question = Level.objects.filter(version=version, order=order).first()
            if existing_question:
                # Shift existing questions down
                Level.objects.filter(version=version, order__gte=order).update(order=models.F('order') + 1)

            # Prepare additional fields for new question types
            additional_fields = {}

            if question_type == 'code_execution':
                additional_fields.update({
                    'programming_language': request.POST.get('programming_language', 'python'),
                    'starter_code': request.POST.get('starter_code', ''),
                    'test_cases': _parse_json_field(request.POST.get('test_cases', '[]'))
                })

            elif question_type == 'drag_drop':
                additional_fields.update({
                    'drag_items': _parse_json_field(request.POST.get('drag_items', '[]')),
                    'drop_zones': _parse_json_field(request.POST.get('drop_zones', '[]')),
                    'correct_matches': _parse_json_field(request.POST.get('correct_matches', '[]'))
                })

            # Create the question
            question = Level.objects.create(
                name=name,
                description=description,
                question_type=question_type,
                correct_answer=correct_answer,
                choices=choices,
                order=order,
                score=score,
                is_bonus=is_bonus,
                explanation=explanation,
                version=version,
                **additional_fields
            )

            # Recalculate scores automatically
            version.calculate_automatic_scores()

            messages.success(request, f'Question "{name}" created successfully')

        except Exception as e:
            messages.error(request, f'Error creating question: {str(e)}')

    return redirect('admin_test_version_detail', version_id=version_id)


@admin_required
def admin_edit_question(request, version_id, question_id):
    """Edit an existing question"""

    version = get_object_or_404(TestVersion, id=version_id)
    question = get_object_or_404(Level, id=question_id, version=version)

    if request.method == 'POST':
        # Similar logic to create_question but for updating
        try:
            question.name = request.POST.get('name', '').strip()
            question.description = request.POST.get('description', '').strip()
            question.explanation = request.POST.get('explanation', '').strip()
            question.is_bonus = request.POST.get('is_bonus') == 'true'
            question.score = int(request.POST.get('score', 5))

            # Handle answer updates based on question type
            if question.question_type == 'text':
                question.correct_answer = request.POST.get('correct_answer_text', '').strip()
            elif question.question_type == 'multiple_choice':
                correct_choice = request.POST.get('correct_choice', '')
                question.correct_answer = correct_choice
                question.choices = {
                    'A': request.POST.get('choice_a', '').strip(),
                    'B': request.POST.get('choice_b', '').strip(),
                    'C': request.POST.get('choice_c', '').strip(),
                    'D': request.POST.get('choice_d', '').strip()
                }
            elif question.question_type == 'true_false':
                question.correct_answer = request.POST.get('correct_tf', '')

            question.save()

            # Recalculate scores
            version.calculate_automatic_scores()

            messages.success(request, f'Question "{question.name}" updated successfully')

        except Exception as e:
            messages.error(request, f'Error updating question: {str(e)}')

    return redirect('admin_test_version_detail', version_id=version_id)


@admin_required
def admin_delete_question(request, version_id, question_id):
    """Delete a question"""

    version = get_object_or_404(TestVersion, id=version_id)
    question = get_object_or_404(Level, id=question_id, version=version)

    if request.method == 'POST':
        try:
            question_name = question.name
            question_order = question.order
            question.delete()

            # Shift remaining questions up
            Level.objects.filter(version=version, order__gt=question_order).update(order=models.F('order') - 1)

            # Recalculate scores
            version.calculate_automatic_scores()

            messages.success(request, f'Question "{question_name}" deleted successfully')

        except Exception as e:
            messages.error(request, f'Error deleting question: {str(e)}')

    return redirect('admin_test_version_detail', version_id=version_id)


@admin_required
def admin_recalculate_scores(request, version_id):
    """Recalculate scores for a test version"""

    version = get_object_or_404(TestVersion, id=version_id)

    try:
        # Get target total from request (default 100)
        target_total = int(request.POST.get('target_total', 100))

        # Validate target total
        if target_total < 1 or target_total > 1000:
            messages.error(request, 'Target total must be between 1 and 1000 points')
            return redirect('admin_test_version_detail', version_id=version_id)

        # Recalculate with custom target
        version.calculate_automatic_scores(target_total)

        # Get updated distribution
        distribution = version.get_score_distribution()

        messages.success(request,
            f'Scores recalculated for version "{version.name}". '
            f'Normal questions: {distribution["normal_total"]} points, '
            f'Bonus questions: {distribution["bonus_total"]} points'
        )

    except Exception as e:
        messages.error(request, f'Error recalculating scores: {str(e)}')

    return redirect('admin_test_version_detail', version_id=version_id)


@admin_required
def admin_scoring_manager(request, version_id):
    """Advanced scoring management interface"""

    version = get_object_or_404(TestVersion, id=version_id)

    if request.method == 'POST':
        action = request.POST.get('action')

        try:
            if action == 'auto_fix':
                # Auto-fix scoring issues
                fixed = version.auto_fix_scoring()
                if fixed:
                    messages.success(request, 'Scoring issues automatically fixed')
                else:
                    messages.info(request, 'No scoring issues found to fix')

            elif action == 'custom_distribute':
                # Custom score distribution
                target_total = int(request.POST.get('target_total', 100))
                version.calculate_automatic_scores(target_total)
                messages.success(request, f'Scores redistributed with target total of {target_total} points')

            elif action == 'manual_scores':
                # Manual score assignment
                question_scores = {}
                for key, value in request.POST.items():
                    if key.startswith('score_'):
                        question_id = int(key.replace('score_', ''))
                        score = int(value)
                        question_scores[question_id] = score

                # Update question scores
                for question_id, score in question_scores.items():
                    question = Level.objects.get(id=question_id, version=version)
                    question.score = score
                    question.save(update_fields=['score'])

                messages.success(request, 'Manual scores updated successfully')

        except Exception as e:
            messages.error(request, f'Error managing scores: {str(e)}')

    # Get current distribution and validation
    distribution = version.get_score_distribution()
    issues = version.validate_scoring()
    questions = version.levels.order_by('order')

    context = {
        'version': version,
        'distribution': distribution,
        'issues': issues,
        'questions': questions,
    }

    return render(request, 'admin/scoring_manager.html', context)


@admin_required
def admin_bulk_import_scenarios(request):
    """Bulk import questions into a selected test version"""

    if request.method != 'POST':
        messages.error(request, 'Invalid request method')
        return redirect('admin_panel:admin_test_versions')

    bulk_csv_file = request.FILES.get('bulk_csv_file')
    version_id = request.POST.get('version_id')

    if not bulk_csv_file:
        messages.error(request, 'Please select a CSV file')
        return redirect('admin_panel:admin_test_versions')

    if not version_id:
        messages.error(request, 'Please select a test version')
        return redirect('admin_panel:admin_test_versions')

    if not bulk_csv_file.name.endswith('.csv'):
        messages.error(request, 'Please upload a CSV file')
        return redirect('admin_panel:admin_test_versions')

    try:
        # Get the test version
        version = get_object_or_404(TestVersion, id=version_id)

        # Read CSV file
        file_data = bulk_csv_file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(file_data))

        imported_count = 0
        errors = []

        for row_num, row in enumerate(csv_reader, start=2):
            try:
                # Validate and sanitize input
                title = validate_and_sanitize_input(row.get('Title', '').strip(), max_length=200)
                description = validate_and_sanitize_input(row.get('Description', '').strip(), max_length=1000)
                question_type = row.get('question_type', 'text').strip().lower()
                answer = validate_and_sanitize_input(row.get('Answer', '').strip(), max_length=500)
                choices_str = row.get('choices', '').strip()
                points = validate_numeric_input(row.get('points', '5'), min_val=1, max_val=100)
                is_bonus = row.get('is_bonus', 'false').strip().lower() == 'true'
                order = validate_numeric_input(row.get('order', str(imported_count + 1)), min_val=1)
                explanation = validate_and_sanitize_input(row.get('explanation', '').strip(), max_length=1000)

                if not title or not description or not answer:
                    errors.append(f"Row {row_num}: Missing required fields (Title, Description, Answer)")
                    continue

                if question_type not in ['text', 'multiple_choice', 'true_false']:
                    errors.append(f"Row {row_num}: Invalid question type '{question_type}'")
                    continue

                # Parse choices for multiple choice questions
                choices = {}
                if question_type == 'multiple_choice' and choices_str:
                    try:
                        for choice in choices_str.split('|'):
                            if ':' in choice:
                                key, value = choice.split(':', 1)
                                choices[key.strip()] = value.strip()
                    except Exception:
                        errors.append(f"Row {row_num}: Invalid choices format")
                        continue
                elif question_type == 'true_false':
                    choices = {'A': 'True', 'B': 'False'}

                # Create the question
                Level.objects.create(
                    name=title,
                    description=description,
                    version=version,
                    question_type=question_type,
                    correct_answer=answer,
                    choices=choices if choices else None,
                    score=points,
                    is_bonus=is_bonus,
                    order=order,
                    explanation=explanation
                )

                imported_count += 1

            except ValidationError as e:
                errors.append(f"Row {row_num}: {str(e)}")
            except Exception as e:
                errors.append(f"Row {row_num}: {str(e)}")

        # Show results
        if imported_count > 0:
            messages.success(request, f'Successfully imported {imported_count} questions into "{version.name}"')

        if errors:
            error_msg = f"Import completed with {len(errors)} errors:\n" + "\n".join(errors[:10])
            if len(errors) > 10:
                error_msg += f"\n... and {len(errors) - 10} more errors"
            messages.warning(request, error_msg)

        if imported_count == 0 and errors:
            messages.error(request, 'No questions were imported due to errors')

    except Exception as e:
        messages.error(request, f'Error processing CSV file: {str(e)}')

    return redirect('admin_panel:admin_test_versions')


@admin_required
def admin_export_all_scenarios(request):
    """Export all scenarios to a ZIP file containing CSV files"""

    try:
        import zipfile
        from io import BytesIO

        # Create a ZIP file in memory
        zip_buffer = BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Get all test versions
            versions = TestVersion.objects.all().order_by('name')

            for version in versions:
                # Create CSV content for each version
                csv_buffer = io.StringIO()
                writer = csv.writer(csv_buffer)

                # Write header
                writer.writerow([
                    'Title', 'Description', 'question_type', 'Answer',
                    'choices', 'points', 'is_bonus', 'order', 'explanation'
                ])

                # Write questions
                for question in version.levels.order_by('order'):
                    choices_str = ''
                    if question.choices:
                        choices_str = '|'.join([f"{k}:{v}" for k, v in question.choices.items()])

                    writer.writerow([
                        question.name,
                        question.description,
                        question.question_type,
                        question.correct_answer,
                        choices_str,
                        question.score,
                        question.is_bonus,
                        question.order,
                        question.explanation or ''
                    ])

                # Add CSV file to ZIP
                csv_content = csv_buffer.getvalue()
                zip_file.writestr(f"{version.name}_questions.csv", csv_content)

        # Create response
        response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
        response['Content-Disposition'] = 'attachment; filename="all_scenarios_export.zip"'

        return response

    except Exception as e:
        messages.error(request, f'Error exporting scenarios: {str(e)}')
        return redirect('admin_panel:admin_test_versions')


@admin_required
def admin_export_data(request):
    """Export comprehensive system data"""

    try:
        import zipfile
        from io import BytesIO
        from django.core import serializers

        # Create a ZIP file in memory
        zip_buffer = BytesIO()

        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:

            # Export all scenarios with questions
            versions = TestVersion.objects.all().order_by('name')
            for version in versions:
                # Create CSV content for each version
                csv_buffer = io.StringIO()
                writer = csv.writer(csv_buffer)

                # Write header
                writer.writerow([
                    'Title', 'Description', 'question_type', 'Answer',
                    'choices', 'points', 'is_bonus', 'order', 'explanation'
                ])

                # Write questions
                for question in version.levels.order_by('order'):
                    choices_str = ''
                    if question.choices:
                        choices_str = '|'.join([f"{k}:{v}" for k, v in question.choices.items()])

                    writer.writerow([
                        question.name,
                        question.description,
                        question.question_type,
                        question.correct_answer,
                        choices_str,
                        question.score,
                        question.is_bonus,
                        question.order,
                        question.explanation or ''
                    ])

                # Add CSV file to ZIP
                csv_content = csv_buffer.getvalue()
                zip_file.writestr(f"scenarios/{version.name}_questions.csv", csv_content)

            # Export users data
            users_buffer = io.StringIO()
            writer = csv.writer(users_buffer)
            writer.writerow(['Username', 'Email', 'First Name', 'Last Name', 'Is Staff', 'Is Active', 'Date Joined'])

            for user in User.objects.all().order_by('username'):
                writer.writerow([
                    user.username,
                    user.email,
                    user.first_name,
                    user.last_name,
                    user.is_staff,
                    user.is_active,
                    user.date_joined.isoformat()
                ])

            zip_file.writestr("users/users_export.csv", users_buffer.getvalue())

            # Export instances data
            instances_buffer = io.StringIO()
            writer = csv.writer(instances_buffer)
            writer.writerow(['Name', 'Version', 'Created By', 'Created At', 'Registration Open', 'Is Active'])

            for instance in ExamInstance.objects.all().order_by('created_at'):
                writer.writerow([
                    instance.name,
                    instance.version.name,
                    instance.created_by.username,
                    instance.created_at.isoformat(),
                    instance.registration_open,
                    instance.is_active
                ])

            zip_file.writestr("instances/instances_export.csv", instances_buffer.getvalue())

            # Export system configuration
            config_buffer = io.StringIO()
            writer = csv.writer(config_buffer)
            writer.writerow(['Setting', 'Value'])

            app_config = AppConfig.objects.first()
            if app_config:
                writer.writerow(['Host', app_config.host])
                writer.writerow(['Port', app_config.port])
                writer.writerow(['Debug', app_config.debug])
                writer.writerow(['Exam Paused', app_config.exam_paused])
                writer.writerow(['Export Completed', app_config.export_completed])

            zip_file.writestr("config/system_config.csv", config_buffer.getvalue())

        # Create response
        response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
        response['Content-Disposition'] = 'attachment; filename="kernelios_system_export.zip"'

        messages.success(request, 'System data exported successfully')
        return response

    except Exception as e:
        messages.error(request, f'Error exporting system data: {str(e)}')
        return redirect('admin_panel:admin_dashboard')
