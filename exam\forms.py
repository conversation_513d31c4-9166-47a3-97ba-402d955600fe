from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import TestVersion, ExamInstance, Level

class RegistrationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    instance = forms.ModelChoiceField(
        queryset=ExamInstance.objects.none(),  # Will be set in __init__
        empty_label="Select Simulation Instance",
        required=True
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2', 'instance')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Set queryset for instances - only show instances that allow registration
        self.fields['instance'].queryset = ExamInstance.objects.filter(
            is_active=True,
            registration_open=True
        ).select_related('version')

        # Apply cyber theme styling
        for field_name, field in self.fields.items():
            field.widget.attrs.update({
                'class': 'form-input',
                'placeholder': field.label
            })

        # Make password fields use PasswordInput as required
        self.fields['password1'].widget = forms.PasswordInput(attrs={
            'class': 'form-input',
            'placeholder': 'Password'
        })
        self.fields['password2'].widget = forms.PasswordInput(attrs={
            'class': 'form-input',
            'placeholder': 'Confirm Password'
        })

class AnswerForm(forms.Form):
    answer = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-input',
            'placeholder': 'Enter your answer...',
            'autocomplete': 'off'
        }),
        required=True
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure answer field is always masked
        self.fields['answer'].widget.attrs.update({
            'type': 'password',
            'autocomplete': 'new-password'
        })

class LoginForm(forms.Form):
    username = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Username'
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-input',
            'placeholder': 'Password'
        })
    )
