{% extends 'exam/base.html' %}

{% block title %}Simulation Dashboard - {{ player.version.name }}{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Main Dashboard Content -->
    <div class="dashboard-main">
        <!-- Header Section -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">{{ player.version.name }} - Simulation Dashboard</h1>
        </div>

        <!-- Questions List -->
        <div class="questions-section">
            <div class="terminal-window">
                <div class="terminal-header">
                    <div class="terminal-dots">
                        <div class="terminal-dot red"></div>
                        <div class="terminal-dot yellow"></div>
                        <div class="terminal-dot green"></div>
                    </div>
                    <div class="terminal-title">question_list.py</div>
                </div>
                <div class="terminal-content">
                    <div class="questions-header">
                        <h2>📝 Questions</h2>
                        <div class="progress-info">
                            <span>Progress: {{ completed_levels|length }}/{{ levels|length }}</span>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {% widthratio completed_levels|length levels|length 100 %}%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="questions-list">
                        {% for level in levels %}
                        <div class="question-item {% if level.id in completed_levels %}completed{% elif level == current_level %}active{% else %}locked{% endif %}">
                            <div class="question-status">
                                {% if level.id in completed_levels %}
                                    <span class="status-icon completed">✓</span>
                                {% elif level == current_level %}
                                    <span class="status-icon active">🎯</span>
                                {% else %}
                                    <span class="status-icon locked">🔒</span>
                                {% endif %}
                            </div>

                            <div class="question-info">
                                <div class="question-header">
                                    <span class="question-number">{{ level.order }}.</span>
                                    <span class="question-title">{{ level.name }}</span>
                                    {% if level.is_bonus %}
                                        <span class="bonus-badge">BONUS</span>
                                    {% endif %}
                                </div>
                                <div class="question-meta">
                                    <span class="question-points">{{ level.score }} points</span>
                                </div>
                            </div>

                            <div class="question-actions">
                                {% if level.id in completed_levels %}
                                    <button onclick="showQuestion({{ level.id }}, true)" class="btn btn-secondary btn-sm">
                                        📖 Review
                                    </button>
                                {% elif level == current_level %}
                                    <button onclick="showQuestion({{ level.id }})" class="btn btn-primary btn-sm">
                                        🎯 Answer
                                    </button>
                                {% else %}
                                    <button onclick="showQuestion({{ level.id }}, true)" class="btn btn-terminal btn-sm">
                                        👁️ Preview
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="dashboard-actions">
            <a href="{% url 'scoreboard' %}" class="btn btn-primary">
                🏆 Leaderboard
            </a>
            <a href="{% url 'score_breakdown' %}" class="btn btn-secondary">
                📊 Analytics
            </a>
        </div>
    </div>

    <!-- Side Progress Terminal -->
    <div class="progress-sidebar">
        <div class="terminal-window terminal-md">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">progress_monitor.sh</div>
            </div>
            <div class="terminal-content" id="progressTerminal">
                <div><span class="terminal-prompt">student@kernelios:~$</span> <span class="terminal-text">./progress_monitor.sh</span></div>
                <div class="terminal-line">📊 Exam Session:</div>
                <div class="terminal-line">├─ Version: <span style="color: var(--primary);">{{ player.version.name }}</span></div>
                <div class="terminal-line">├─ Score: <span style="color: var(--primary);">{{ player.score|floatformat:1 }} pts</span></div>
                <div class="terminal-line">├─ Progress: <span style="color: var(--success);">{{ completed_levels|length }}/{{ levels|length }}</span></div>
                <div class="terminal-line">├─ Completion: <span style="color: var(--success);">{% widthratio completed_levels|length levels|length 100 %}%</span></div>
                {% if player.start_time %}
                <div class="terminal-line">├─ Duration: <span style="color: var(--text-terminal-secondary);">{{ player.start_time|timesince }}</span></div>
                {% endif %}
                <div class="terminal-line">└─ Status: <span style="color: var(--text-terminal);">ACTIVE</span></div>
                <div class="terminal-line"></div>
                <div class="terminal-line">🎯 Current Question:</div>
                {% if current_level %}
                <div class="terminal-line">├─ Number: <span style="color: var(--primary);">{{ current_level.order }}</span></div>
                <div class="terminal-line">├─ Name: <span style="color: var(--primary);">{{ current_level.name }}</span></div>
                <div class="terminal-line">├─ Points: <span style="color: var(--primary);">{{ current_level.score }}</span></div>
                {% if current_level.is_bonus %}
                <div class="terminal-line">└─ Type: <span style="color: var(--accent);">BONUS</span></div>
                {% else %}
                <div class="terminal-line">└─ Type: <span style="color: var(--text-terminal);">Standard</span></div>
                {% endif %}
                {% else %}
                <div class="terminal-line">└─ All questions completed!</div>
                {% endif %}
                <div class="terminal-line"></div>
                <div class="terminal-line">📈 Performance:</div>
                <div class="terminal-line">├─ Rank: <span style="color: var(--primary);">#{{ player.rank|default:"--" }}</span></div>
                <div class="terminal-line">└─ Level: <span style="color: var(--text-terminal);">{% if player.score < 50 %}Novice{% elif player.score < 100 %}Intermediate{% else %}Expert{% endif %}</span></div>
                <div style="margin-top: 0.5rem;"><span class="terminal-cursor"></span></div>
            </div>
        </div>

        <!-- Network Monitor -->
        <div class="terminal-window terminal-sm" style="margin-top: 1rem;">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">network.log</div>
            </div>
            <div class="terminal-content" id="networkMonitor">
                <div class="terminal-line">🌐 Network Status:</div>
                <div class="terminal-line">├─ Connection: <span style="color: var(--success);">SECURE</span></div>
                <div class="terminal-line">├─ Latency: <span style="color: var(--success);">12ms</span></div>
                <div class="terminal-line">├─ Encryption: <span style="color: var(--success);">AES-256</span></div>
                <div class="terminal-line">└─ Firewall: <span style="color: var(--success);">ACTIVE</span></div>
            </div>
        </div>
    </div>
</div>

<!-- Cyber Question Modal -->
<div id="questionModal" class="question-modal">
    <div class="modal-backdrop"></div>
    <div class="modal-container">
        <div class="terminal-window terminal-xl">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title" id="modalTerminalTitle">challenge_interface.py</div>
                <button onclick="closeQuestion()" class="modal-close">✕</button>
            </div>
            <div class="terminal-content">
                <div class="question-container">
                    <div class="question-header">
                        <h2 id="questionTitle" class="question-title"></h2>
                        <div class="question-meta">
                            <span class="question-points" id="questionPoints"></span>
                            <span class="question-type" id="questionType">Security Challenge</span>
                        </div>
                    </div>

                    <div class="question-content">
                        <div id="questionDescription" class="question-description"></div>
                        <div id="attemptsDisplay" class="attempts-display"></div>
                    </div>

                    <form id="answerForm" class="answer-form" method="post">
                        {% csrf_token %}
                        <input type="hidden" id="levelId" name="level_id">

                        <div class="answer-section">
                            <label class="form-label">🔐 Your Solution:</label>
                            <div class="input-container">
                                <span class="input-prompt">></span>
                                <input type="password" name="answer" class="form-input answer-input"
                                       placeholder="Enter your answer..." required>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                🚀 Submit Solution
                            </button>
                            <button type="button" onclick="closeQuestion()" class="btn btn-secondary">
                                ❌ Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Side Progress Panel in Modal -->
        <div class="modal-sidebar">
            <div class="terminal-window terminal-sm">
                <div class="terminal-header">
                    <div class="terminal-dots">
                        <div class="terminal-dot red"></div>
                        <div class="terminal-dot yellow"></div>
                        <div class="terminal-dot green"></div>
                    </div>
                    <div class="terminal-title">challenge_stats.log</div>
                </div>
                <div class="terminal-content" id="challengeStats">
                    <div class="terminal-line">📊 Challenge Stats:</div>
                    <div class="terminal-line">├─ Difficulty: <span style="color: var(--warning);">Medium</span></div>
                    <div class="terminal-line">├─ Success Rate: <span style="color: var(--success);">78%</span></div>
                    <div class="terminal-line">├─ Avg Time: <span style="color: var(--primary);">3.2 min</span></div>
                    <div class="terminal-line">└─ Category: <span style="color: var(--text-terminal);">Web Security</span></div>
                    <div class="terminal-line"></div>
                    <div class="terminal-line">💡 Hint System:</div>
                    <div class="terminal-line">├─ Available: <span style="color: var(--success);">Yes</span></div>
                    <div class="terminal-line">├─ Cost: <span style="color: var(--warning);">-0.5 pts</span></div>
                    <div class="terminal-line">└─ Remaining: <span style="color: var(--primary);">3</span></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Dashboard Layout */
.dashboard-container {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.dashboard-main {
    min-height: calc(100vh - 120px);
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 2rem;
    color: var(--primary);
    text-align: center;
    margin: 0;
}

/* Questions Section */
.questions-section {
    margin-bottom: 2rem;
}

.questions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--terminal-border);
}

.questions-header h2 {
    color: var(--primary);
    font-family: 'Orbitron', sans-serif;
    margin: 0;
}

.progress-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.progress-info span {
    font-family: 'Fira Code', monospace;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.progress-bar {
    width: 200px;
    height: 8px;
    background: var(--dark-card);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--terminal-border);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--success));
    transition: width 0.5s ease;
}

.questions-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.question-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--terminal-bg);
    border: 1px solid var(--terminal-border);
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
}

.question-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    transition: all 0.3s ease;
}

.question-item.completed::before {
    background: var(--success);
}

.question-item.active::before {
    background: var(--primary);
}

.question-item.locked::before {
    background: var(--text-muted);
}

.question-item:hover {
    background: var(--dark-lighter);
    border-color: var(--primary);
}

.question-item.completed {
    border-color: var(--success);
}

.question-item.active {
    border-color: var(--primary);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.2);
}

.question-item.locked {
    opacity: 0.7;
}

.question-status {
    flex-shrink: 0;
}

.status-icon {
    font-size: 1.25rem;
}

.status-icon.completed {
    color: var(--success);
}

.status-icon.active {
    color: var(--primary);
}

.status-icon.locked {
    color: var(--text-muted);
}

.question-info {
    flex: 1;
}

.question-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.question-number {
    font-family: 'Fira Code', monospace;
    font-weight: 700;
    color: var(--primary);
}

.question-title {
    color: var(--text-primary);
    font-weight: 600;
}

.bonus-badge {
    background: var(--accent);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.question-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.question-points {
    font-family: 'Fira Code', monospace;
    color: var(--primary);
    font-weight: 600;
}

.question-actions {
    flex-shrink: 0;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Dashboard Actions */
.dashboard-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Progress Sidebar */
.progress-sidebar {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.terminal-line {
    margin: 0.25rem 0;
    color: var(--text-terminal);
    font-size: 0.8rem;
    line-height: 1.4;
}
</style>

<style>
/* Question Modal Styles */
.question-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.modal-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 1rem;
    max-width: 1000px;
    width: 95%;
    max-height: 90vh;
    z-index: 2001;
    transition: transform 0.3s ease;
}

@keyframes modalSlideIn {
    0% {
        transform: translate(-50%, -50%) scale(0.9);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes successPulse {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

@keyframes errorPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.modal-close {
    position: absolute;
    top: 12px;
    right: 16px;
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.25rem;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
    z-index: 10;
}

.modal-close:hover {
    background: var(--terminal-border);
    color: var(--text-primary);
}

.question-container {
    padding: 1rem;
}

.question-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--terminal-border);
}

.question-title {
    color: var(--primary);
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.question-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.question-points {
    background: var(--primary);
    color: var(--dark);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-weight: 700;
    font-size: 0.875rem;
}

.question-type {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-family: 'Fira Code', monospace;
}

.question-description {
    color: var(--text-primary);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.attempts-display {
    background: rgba(255, 61, 0, 0.1);
    border: 1px solid var(--accent);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.attempts-header {
    color: var(--accent);
    font-weight: 700;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.attempts-text {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

.answer-section {
    margin-bottom: 2rem;
}

.answer-input {
    font-family: 'Fira Code', monospace !important;
    font-size: 1rem !important;
}

.form-actions {
    display: flex;
    gap: 1rem;
}

.modal-sidebar {
    display: flex;
    flex-direction: column;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .progress-sidebar {
        position: static;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .modal-container {
        grid-template-columns: 1fr;
        max-width: 800px;
    }

    .modal-sidebar {
        display: none;
    }
}

@media (max-width: 768px) {
    .questions-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .progress-info {
        align-items: flex-start;
    }

    .progress-bar {
        width: 150px;
    }

    .question-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .question-header {
        flex-wrap: wrap;
    }

    .progress-sidebar {
        grid-template-columns: 1fr;
    }

    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .dashboard-title {
        font-size: 1.5rem;
    }

    .question-item {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .question-actions {
        width: 100%;
    }

    .btn-sm {
        width: 100%;
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .modal-container {
        width: 98%;
        max-height: 95vh;
        grid-template-columns: 1fr;
    }

    .modal-sidebar {
        display: block;
        order: -1;
    }

    .question-header {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
}

@media (max-width: 320px) {
    .dashboard-title {
        font-size: 1.25rem;
    }

    .question-item {
        padding: 0.5rem;
    }

    .btn-sm {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }
}
</style>

<script>
function showQuestion(levelId, isPreview = false) {
    fetch(`/question/${levelId}/?preview=${isPreview}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showErrorNotification(data.error);
                return;
            }

            // Update modal title
            const modalTitle = isPreview ? 'preview_mode.py' : 'active_challenge.py';
            document.getElementById('modalTerminalTitle').textContent = modalTitle;

            // Update question title and meta
            const titlePrefix = isPreview ? '👁️ PREVIEW: ' : '🎯 CHALLENGE: ';
            document.getElementById('questionTitle').textContent = `${titlePrefix}${data.name}`;

            // Update question meta
            document.getElementById('questionPoints').textContent = `${data.score} pts`;
            document.getElementById('questionType').textContent = data.is_bonus ? 'BONUS Challenge' : 'Security Challenge';

            // Update description
            document.getElementById('questionDescription').innerHTML = `
                <div class="terminal-line"><span class="terminal-prompt">challenge@${data.order}:~$</span> <span class="terminal-text">cat description.txt</span></div>
                <div style="margin: 1rem 0; padding: 1rem; background: rgba(255, 215, 0, 0.05); border-left: 3px solid var(--primary); border-radius: 4px;">
                    ${data.description}
                </div>
            `;

            document.getElementById('levelId').value = levelId;

            const answerForm = document.getElementById('answerForm');
            const attemptsDisplay = document.getElementById('attemptsDisplay');

            if (isPreview) {
                // Hide answer form for preview mode
                answerForm.style.display = 'none';
                attemptsDisplay.innerHTML = `
                    <div style="background: rgba(255, 215, 0, 0.1); border: 1px solid var(--primary); border-radius: 8px; padding: 1rem; margin: 1rem 0; text-align: center;">
                        <div style="color: var(--primary); font-weight: 700; margin-bottom: 0.5rem;">📖 Preview Mode</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Complete the current challenge to unlock this question for answering.</div>
                    </div>
                `;
            } else {
                // Show answer form for active questions
                answerForm.style.display = 'block';
                attemptsDisplay.innerHTML = '';

                // Show attempts if any exist
                if (data.attempts > 0) {
                    updateAttemptsDisplay(data.attempts);
                }

                // Update answer input based on question type
                updateAnswerInput(data);
            }

            // Show modal with smooth animation
            const modal = document.getElementById('questionModal');
            modal.style.display = 'block';
            modal.style.opacity = '0';

            // Force reflow to ensure display:block is applied
            modal.offsetHeight;

            // Apply smooth transition
            modal.style.transition = 'opacity 0.3s ease';
            modal.style.opacity = '1';

            // Focus on answer input if not preview
            if (!isPreview) {
                setTimeout(() => {
                    const answerInput = document.querySelector('input[name="answer"]');
                    if (answerInput) answerInput.focus();
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showErrorNotification('Failed to load question');
        });
}

function updateAnswerInput(questionData) {
    const answerSection = document.querySelector('.answer-section');
    const questionType = questionData.question_type || 'text';

    let inputHtml = '';

    if (questionType === 'multiple_choice') {
        // Multiple choice question
        inputHtml = `
            <label class="form-label">🔘 Select your answer:</label>
            <div class="choices-container" style="display: grid; gap: 0.75rem; margin: 1rem 0;">
        `;

        if (questionData.choices) {
            Object.entries(questionData.choices).forEach(([key, value]) => {
                inputHtml += `
                    <label style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; cursor: pointer; transition: all 0.3s ease;" class="choice-option">
                        <input type="radio" name="answer" value="${key}" style="margin: 0; transform: scale(1.2);">
                        <div>
                            <strong style="color: var(--primary); margin-right: 0.5rem;">${key}:</strong>
                            <span style="color: var(--text-primary);">${value}</span>
                        </div>
                    </label>
                `;
            });
        }

        inputHtml += '</div>';

    } else if (questionType === 'true_false') {
        // True/False question
        inputHtml = `
            <label class="form-label">✅ Select True or False:</label>
            <div class="choices-container" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin: 1rem 0;">
                <label style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: rgba(0, 255, 65, 0.1); border: 2px solid rgba(0, 255, 65, 0.3); border-radius: 0.5rem; cursor: pointer; transition: all 0.3s ease;" class="choice-option">
                    <input type="radio" name="answer" value="A" style="margin: 0; transform: scale(1.2);">
                    <span style="color: var(--success); font-weight: bold;">✅ True</span>
                </label>
                <label style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; padding: 1rem; background: rgba(255, 0, 64, 0.1); border: 2px solid rgba(255, 0, 64, 0.3); border-radius: 0.5rem; cursor: pointer; transition: all 0.3s ease;" class="choice-option">
                    <input type="radio" name="answer" value="B" style="margin: 0; transform: scale(1.2);">
                    <span style="color: var(--error); font-weight: bold;">❌ False</span>
                </label>
            </div>
        `;

    } else if (questionType === 'drag_drop') {
        // Drag and Drop question
        inputHtml = `
            <label class="form-label">🎯 Drag and Drop:</label>
            <div id="dragDropContainer" class="drag-drop-container">
                <!-- Drag and drop interface will be created here -->
            </div>
            <input type="hidden" name="answer" id="dragDropAnswer" required>
        `;
    } else if (questionType === 'code_execution') {
        // Code Execution question
        inputHtml = `
            <label class="form-label">💻 Code Execution:</label>
            <div class="code-editor-container">
                <textarea id="codeEditor" name="answer" class="code-editor"
                          placeholder="Write your code here..." required
                          style="width: 100%; height: 200px; background: #1a1a1a; color: #00ff41;
                                 border: 2px solid rgba(255, 215, 0, 0.3); border-radius: 0.5rem;
                                 padding: 1rem; font-family: 'JetBrains Mono', monospace;
                                 font-size: 0.9rem; resize: vertical;"></textarea>
            </div>
            <div class="code-actions" style="margin-top: 0.5rem; display: flex; gap: 0.5rem;">
                <button type="button" onclick="runCode()" class="btn btn-secondary btn-sm">
                    ▶️ Run Code
                </button>
                <button type="button" onclick="clearCode()" class="btn btn-secondary btn-sm">
                    🗑️ Clear
                </button>
            </div>
            <div id="codeOutput" class="code-output" style="margin-top: 1rem; padding: 1rem;
                 background: rgba(0, 0, 0, 0.5); border-radius: 0.5rem;
                 font-family: 'JetBrains Mono', monospace; font-size: 0.8rem;
                 color: var(--text-secondary); display: none;">
                <div class="output-header" style="color: var(--primary); margin-bottom: 0.5rem;">Output:</div>
                <pre id="outputContent"></pre>
            </div>
        `;
    } else {
        // Text answer (default)
        inputHtml = `
            <label class="form-label">🔐 Your Solution:</label>
            <div class="input-container">
                <span class="input-prompt">></span>
                <input type="password" name="answer" class="form-input answer-input"
                       placeholder="Enter your answer..." required>
            </div>
        `;
    }

    answerSection.innerHTML = inputHtml;

    // Initialize drag and drop if needed
    if (questionType === 'drag_drop' && questionData.drag_items && questionData.drop_zones) {
        setTimeout(() => {
            initializeDragDrop(questionData);
        }, 100);
    }

    // Add hover effects for choice options
    document.querySelectorAll('.choice-option').forEach(option => {
        option.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 15px rgba(255, 215, 0, 0.2)';
        });

        option.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });

        option.addEventListener('click', function() {
            // Remove selection from other options
            document.querySelectorAll('.choice-option').forEach(opt => {
                opt.style.borderColor = opt.style.borderColor.replace('var(--primary)', 'rgba(255, 255, 255, 0.3)');
                opt.style.background = opt.style.background.replace('rgba(255, 215, 0, 0.1)', 'rgba(255, 255, 255, 0.05)');
            });

            // Highlight selected option
            this.style.borderColor = 'var(--primary)';
            this.style.background = 'rgba(255, 215, 0, 0.1)';
        });
    });
}

function initializeDragDrop(questionData) {
    const container = document.getElementById('dragDropContainer');
    if (!container) return;

    // Create drag and drop interface
    const dragDropQuestion = new DragDropQuestion('dragDropContainer', {
        dragItems: questionData.drag_items || [],
        dropZones: questionData.drop_zones || [],
        correctMatches: questionData.correct_matches || [],
        onAnswerChange: function(matches) {
            // Update hidden input with current matches
            const answerInput = document.getElementById('dragDropAnswer');
            if (answerInput) {
                answerInput.value = JSON.stringify(matches);
            }
        }
    });
}

function runCode() {
    const codeEditor = document.getElementById('codeEditor');
    const outputDiv = document.getElementById('codeOutput');
    const outputContent = document.getElementById('outputContent');

    if (!codeEditor || !outputDiv || !outputContent) return;

    const code = codeEditor.value.trim();
    if (!code) {
        alert('Please enter some code to run.');
        return;
    }

    // Show output section
    outputDiv.style.display = 'block';
    outputContent.textContent = 'Running code...';

    // Simulate code execution (in production, this would call a secure sandbox API)
    setTimeout(() => {
        try {
            // This is a simplified simulation - in production you'd use a secure code execution service
            if (code.includes('print(') || code.includes('console.log(')) {
                outputContent.textContent = 'Code executed successfully!\nOutput: Hello World!';
                outputContent.style.color = 'var(--success)';
            } else {
                outputContent.textContent = 'Code compiled successfully. No output to display.';
                outputContent.style.color = 'var(--text-secondary)';
            }
        } catch (error) {
            outputContent.textContent = 'Error: ' + error.message;
            outputContent.style.color = 'var(--error)';
        }
    }, 1000);
}

function clearCode() {
    const codeEditor = document.getElementById('codeEditor');
    const outputDiv = document.getElementById('codeOutput');

    if (codeEditor) {
        codeEditor.value = '';
    }

    if (outputDiv) {
        outputDiv.style.display = 'none';
    }
}

function closeQuestion() {
    const modal = document.getElementById('questionModal');
    modal.style.opacity = '0';
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function showErrorNotification(message) {
    // Create error notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 120px;
        right: 20px;
        background: linear-gradient(135deg, var(--error), #dc2626);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: var(--shadow-terminal);
        z-index: 3000;
        font-family: 'Fira Code', monospace;
        font-size: 0.875rem;
        max-width: 300px;
        animation: slideInRight 0.3s ease;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <span>❌</span>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

function showSuccessNotification(message) {
    // Create success notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 120px;
        right: 20px;
        background: linear-gradient(135deg, var(--success), #16a34a);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: var(--shadow-terminal);
        z-index: 3000;
        font-family: 'Fira Code', monospace;
        font-size: 0.875rem;
        max-width: 300px;
        animation: slideInRight 0.3s ease;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <span>✅</span>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}

document.getElementById('answerForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const levelId = document.getElementById('levelId').value;

    fetch(`/submit/${levelId}/`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
            return;
        }

        if (data.correct) {
            // Show success animation
            showSuccessAnimation(data.message, data.new_score);

            // Update the score display immediately
            if (data.new_score !== undefined) {
                const scoreElement = document.getElementById('currentScore');
                if (scoreElement) {
                    scoreElement.innerHTML = `<strong>Current Score:</strong> ${data.new_score.toFixed(1)} points`;

                    // Add a brief highlight effect
                    scoreElement.style.background = 'rgba(255, 215, 0, 0.2)';
                    scoreElement.style.borderRadius = '0.5rem';
                    scoreElement.style.padding = '0.5rem';
                    scoreElement.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        scoreElement.style.background = 'transparent';
                        scoreElement.style.padding = '0';
                    }, 2000);
                }
            }

            // Close modal and refresh after animation
            setTimeout(() => {
                closeQuestion();
                if (data.exam_complete) {
                    setTimeout(() => {
                        showSuccessAnimation('🎉 Congratulations! You have completed all questions!');
                        setTimeout(() => location.reload(), 3000);
                    }, 500);
                } else {
                    setTimeout(() => location.reload(), 1000); // Refresh to show updated progress
                }
            }, 2000);
        } else {
            // Show error animation and update attempts display
            showErrorAnimation();

            // Update attempts display immediately
            updateAttemptsDisplay(data.attempts);

            // Clear the answer field for retry
            document.querySelector('input[name="answer"]').value = '';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to submit answer');
    });
});

// Check for pause status every 5 seconds
function checkPauseStatus() {
    fetch('/check-pause-status/')
        .then(response => response.json())
        .then(data => {
            if (data.is_paused) {
                // Reload page to show pause screen
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error checking pause status:', error);
        });
}

// Start checking pause status every 30 seconds (reduced frequency)
setInterval(checkPauseStatus, 30000);

// Check pause status on page load
checkPauseStatus();

// Animation functions
function showSuccessAnimation(message, newScore) {
    const modal = document.getElementById('questionModal');
    const terminalWindow = modal.querySelector('.terminal-window');

    // Create success overlay
    const successOverlay = document.createElement('div');
    successOverlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.95), rgba(34, 197, 94, 0.85));
        border-radius: 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 100;
        animation: successPulse 0.6s ease-out;
    `;

    const scoreText = newScore ? `<div style="font-size: 1.2rem; margin-top: 0.5rem; color: white; font-family: 'JetBrains Mono', monospace;">🎯 Score: ${newScore.toFixed(1)} points</div>` : '';

    successOverlay.innerHTML = `
        <div style="text-align: center; color: white; font-family: 'Orbitron', sans-serif;">
            <div style="font-size: 4rem; margin-bottom: 1rem; animation: bounce 0.6s ease;">🎉</div>
            <div style="font-size: 2rem; font-weight: bold; margin-bottom: 1rem; text-shadow: 0 0 10px rgba(255,255,255,0.5);">CORRECT!</div>
            <div style="font-size: 1.1rem; margin-bottom: 0.5rem; opacity: 0.9;">${message}</div>
            ${scoreText}
        </div>
        <div class="fireworks">
            <div class="firework"></div>
            <div class="firework"></div>
            <div class="firework"></div>
        </div>
    `;

    terminalWindow.style.position = 'relative';
    terminalWindow.appendChild(successOverlay);

    // Remove overlay after animation
    setTimeout(() => {
        if (successOverlay.parentNode) {
            successOverlay.remove();
        }
    }, 2000);
}

function showErrorAnimation() {
    const modal = document.getElementById('questionModal');
    const modalContent = modal.querySelector('div');

    // Add shake animation
    modalContent.style.animation = 'shake 0.5s ease-in-out';
    modalContent.style.background = 'linear-gradient(135deg, rgba(239, 68, 68, 0.1), var(--dark-lighter))';
    modalContent.style.border = '2px solid rgba(239, 68, 68, 0.3)';

    // Create error message
    const errorMessage = document.createElement('div');
    errorMessage.style.cssText = `
        position: absolute;
        top: 1rem;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(239, 68, 68, 0.9);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: bold;
        z-index: 10;
        animation: errorSlide 0.5s ease-out;
    `;
    errorMessage.textContent = '❌ Try again!';

    modalContent.style.position = 'relative';
    modalContent.appendChild(errorMessage);

    // Remove error styling after animation
    setTimeout(() => {
        modalContent.style.animation = '';
        modalContent.style.background = 'var(--dark-lighter)';
        modalContent.style.border = 'none';
        if (errorMessage.parentNode) {
            errorMessage.remove();
        }
    }, 2000);
}

function updateAttemptsDisplay(attempts) {
    const attemptsContainer = document.getElementById('attemptsDisplay');
    if (attemptsContainer && attempts > 0) {
        attemptsContainer.innerHTML = `
            <div class="attempts-display">
                <div class="attempts-header">⚠️ Previous attempts: ${attempts}</div>
                <div class="attempts-text">Each incorrect attempt may reduce your score.</div>
            </div>
        `;
    }
}
</script>

<style>
@keyframes successPulse {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.05); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes shake {
    0%, 100% { transform: translate(-50%, -50%) translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translate(-50%, -50%) translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translate(-50%, -50%) translateX(5px); }
}

@keyframes errorSlide {
    0% { transform: translateX(-50%) translateY(-20px); opacity: 0; }
    100% { transform: translateX(-50%) translateY(0); opacity: 1; }
}

.fireworks {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.firework {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    animation: firework 1.5s ease-out infinite;
}

.firework:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0.1s;
}

.firework:nth-child(2) {
    top: 30%;
    right: 20%;
    animation-delay: 0.3s;
}

.firework:nth-child(3) {
    bottom: 30%;
    left: 50%;
    animation-delay: 0.5s;
}

@keyframes firework {
    0% {
        transform: scale(0);
        opacity: 1;
        box-shadow: 0 0 0 0 #ffd700, 0 0 0 0 #ff6b6b, 0 0 0 0 #4ecdc4;
    }
    50% {
        transform: scale(1);
        opacity: 1;
        box-shadow: 0 0 0 10px transparent, 0 0 0 20px transparent, 0 0 0 30px transparent;
    }
    100% {
        transform: scale(1);
        opacity: 0;
        box-shadow: 0 0 0 15px transparent, 0 0 0 25px transparent, 0 0 0 35px transparent;
    }
}

/* Notification Animations */
@keyframes slideInRight {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    0% { transform: translateX(0); opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}

@keyframes fadeInUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}


</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard animations
    setTimeout(() => {
        // Animate stat items
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'all 0.5s ease';
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });

        // Animate challenge cards
        const challengeCards = document.querySelectorAll('.challenge-card');
        challengeCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.5s ease';
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 50);
            }, 500 + (index * 100));
        });

        // Animate progress terminal
        const progressLines = document.querySelectorAll('#progressTerminal .terminal-line');
        progressLines.forEach((line, index) => {
            setTimeout(() => {
                line.style.opacity = '0';
                line.style.transform = 'translateX(-10px)';
                line.style.transition = 'all 0.3s ease';
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.transform = 'translateX(0)';
                }, 50);
            }, 1000 + (index * 100));
        });
    }, 500);
});
</script>

<!-- Include drag and drop functionality -->
<script src="{% load static %}{% static 'js/drag_drop.js' %}"></script>

{% endblock %}
