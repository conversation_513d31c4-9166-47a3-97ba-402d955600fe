{% extends 'exam/base.html' %}

{% block title %}Login - KERNELiOS{% endblock %}

{% block content %}
<div class="login-container">
    <!-- Main Login Terminal -->
    <div class="login-main">
        <div class="terminal-window terminal-xl">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">secure_login.sh</div>
            </div>
            <div class="terminal-content">
                <div class="login-header">
                    <div class="login-logo">
                        <img src="/static/assets/logo.png" alt="KERNELiOS" style="width: 80px; height: 80px;">
                    </div>
                    <h1 class="login-title">Simulator Portal</h1>
                    <p class="login-subtitle">Sign in to access your simulation dashboard</p>
                </div>

                <div class="login-form-container">
                    <form method="post" class="login-form">
                        {% csrf_token %}

                        <div class="form-group">
                            <label class="form-label">📧 Email Address</label>
                            <div class="input-container">
                                <span class="input-prompt">></span>
                                <input type="text" name="username" class="form-input" placeholder="<EMAIL>" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">🔒 Password</label>
                            <div class="input-container">
                                <span class="input-prompt">></span>
                                <input type="password" name="password" class="form-input" placeholder="••••••••" required>
                            </div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-container">
                                <input type="checkbox" name="remember_me">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password">Forgot password?</a>
                        </div>

                        <button type="submit" class="btn btn-primary btn-login">
                            <span>></span> Login to Simulator
                        </button>
                    </form>

                    <div class="login-footer">
                        <p>Don't have an account? <a href="{% url 'register' %}" class="register-link">Register for simulation</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Security Terminal -->
    <div class="security-terminal">
        <div class="terminal-window terminal-md">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">auth_session.sh</div>
            </div>
            <div class="terminal-content" id="securityTerminal">
                <div><span class="terminal-prompt">kernelios@system:~$</span> <span class="terminal-text">./auth_session.sh</span></div>
                <div class="terminal-line">Initializing secure portal...</div>
                <div class="terminal-line">🔐 Encryption: <span style="color: var(--success);">AES-256</span></div>
                <div class="terminal-line">🛡️ Firewall: <span style="color: var(--success);">ACTIVE</span></div>
                <div class="terminal-line">🔍 Threat Detection: <span style="color: var(--success);">MONITORING</span></div>
                <div class="terminal-line">📊 Session Security: <span style="color: var(--primary);">MAXIMUM</span></div>
                <div class="terminal-line">⚡ Connection: <span style="color: var(--success);">SECURE</span></div>
                <div class="terminal-line">🎯 Ready for authentication...</div>
                <div style="margin-top: 0.5rem;"><span class="terminal-cursor"></span></div>
            </div>
        </div>
    </div>
</div>

<style>
.login-container {
    min-height: calc(100vh - 100px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.login-main {
    max-width: 500px;
    width: 100%;
    z-index: 10;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--terminal-border);
}

.login-logo {
    margin-bottom: 1rem;
}

.login-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.login-form-container {
    padding: 0 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-prompt {
    color: var(--text-terminal-secondary);
    font-family: 'Fira Code', monospace;
    font-weight: 600;
    margin-right: 0.5rem;
    font-size: 1rem;
}

.form-input {
    flex: 1;
    margin-bottom: 0;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.875rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary);
}

.forgot-password {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--primary-light);
}

.btn-login {
    width: 100%;
    font-family: 'Fira Code', monospace;
    font-size: 1rem;
    margin-bottom: 2rem;
}

.login-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--terminal-border);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.register-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
}

.register-link:hover {
    color: var(--primary-light);
}

.security-terminal {
    position: fixed;
    top: 20%;
    right: 5%;
    z-index: 1;
    opacity: 0.9;
    pointer-events: none;
}

.terminal-line {
    margin: 0.3rem 0;
    color: var(--text-terminal);
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
    }

    .security-terminal {
        display: none;
    }

    .login-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .login-main {
        max-width: 100%;
        margin: 0 0.5rem;
    }

    .login-form-container {
        padding: 2rem 1.5rem;
    }

    .login-title {
        font-size: 1.25rem;
    }

    .form-group {
        margin-bottom: 1.25rem;
    }

    .btn-login {
        font-size: 0.9rem;
        padding: 0.75rem 1.5rem;
    }

    .form-input {
        font-size: 0.9rem;
        padding: 0.75rem;
    }
}

@media (max-width: 320px) {
    .login-form-container {
        padding: 1.5rem 1rem;
    }

    .login-title {
        font-size: 1.1rem;
    }

    .btn-login {
        font-size: 0.85rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate security terminal
    setTimeout(() => {
        const lines = document.querySelectorAll('.terminal-line');
        lines.forEach((line, index) => {
            setTimeout(() => {
                line.style.opacity = '0';
                line.style.transform = 'translateX(-10px)';
                line.style.transition = 'all 0.5s ease';
                setTimeout(() => {
                    line.style.opacity = '1';
                    line.style.transform = 'translateX(0)';
                }, 50);
            }, index * 200);
        });
    }, 1000);

    // Focus first input
    const firstInput = document.querySelector('input[name="username"]');
    if (firstInput) {
        setTimeout(() => firstInput.focus(), 500);
    }
});
</script>
{% endblock %}
