<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KERNELiOS Admin Panel{% endblock %}</title>
    <link rel="icon" type="image/png" href="/static/assets/logo.png">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600;700&family=Orbitron:wght@500;600;700&family=Fira+Code:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* KernelIOS Admin Terminal UI System */
        :root {
            /* Primary Colors - Cyber Gold */
            --primary: #FFD700;
            --primary-light: #FFEB3B;
            --primary-dark: #FFC107;
            --primary-hover: #FFEE58;

            /* Secondary Colors - Deep Purple */
            --secondary: #4B0082;
            --secondary-light: #6A0DAD;
            --secondary-dark: #2E004D;
            --secondary-hover: #5D00A0;

            /* Dark Theme Base */
            --dark: #0A0A0A;
            --dark-lighter: #121212;
            --dark-light: #1A1A1A;
            --dark-card: #161616;
            --dark-terminal: #0D1117;
            --dark-terminal-header: #1C2128;

            /* Gray Scale */
            --gray-750: #3F3F46;
            --gray-850: #27272A;
            --gray-900: #1A1A1A;

            /* Accent Colors */
            --accent: #FF3D00;
            --accent-light: #FF5722;
            --success: #00FF41;
            --warning: #FFA500;
            --error: #FF0040;

            /* Text Colors */
            --text-primary: #FFFFFF;
            --text-secondary: #CCCCCC;
            --text-muted: #999999;
            --text-terminal: #00FF41;
            --text-terminal-secondary: #FFD700;

            /* Terminal Colors */
            --terminal-bg: #0D1117;
            --terminal-border: #30363D;
            --terminal-header: #1C2128;
            --terminal-dot-red: #FF5F56;
            --terminal-dot-yellow: #FFBD2E;
            --terminal-dot-green: #27CA3F;

            /* Shadows and Effects */
            --shadow-terminal: 0 8px 32px rgba(0, 0, 0, 0.6);
            --glow-primary: 0 0 20px rgba(255, 215, 0, 0.3);
            --glow-terminal: 0 0 15px rgba(0, 255, 65, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, var(--dark-lighter) 50%, var(--dark) 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* Background Animation - Match Main Site Exactly */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 61, 0, 0.02) 0%, transparent 50%);
            z-index: -2;
            animation: cyberPulse 8s ease-in-out infinite alternate;
        }

        @keyframes cyberPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }

        /* Cyber Grid Background - Match Main Site Exactly */
        .cyber-grid-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(255, 215, 0, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 215, 0, 0.03) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(40px, 40px); }
        }

        /* Navigation - Match main site exactly */
        .admin-navbar {
            background: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(18, 18, 18, 0.95) 100%);
            backdrop-filter: blur(15px);
            border-bottom: 2px solid var(--terminal-border);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            overflow: visible;
        }

        .admin-navbar::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary) 20%,
                var(--primary) 80%,
                transparent 100%);
            animation: borderGlow 3s ease-in-out infinite alternate;
        }

        @keyframes borderGlow {
            0% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .admin-navbar .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            min-height: 70px;
        }

        .admin-logo {
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .admin-logo:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        }

        .admin-logo img {
            height: 60px;
            width: auto;
            filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.2));
        }

        .admin-nav-links {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            flex: 1;
            justify-content: center;
            min-width: 0;
            overflow: hidden;
        }

        .admin-nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            padding: 0.4rem 0.6rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid transparent;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .admin-nav-link:hover {
            color: var(--primary);
            background: rgba(255, 215, 0, 0.1);
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 2px 15px rgba(255, 215, 0, 0.2);
            transform: translateY(0px);
        }

        .admin-nav-link.active {
            color: var(--primary);
            background: rgba(255, 215, 0, 0.15);
            border-color: rgba(255, 215, 0, 0.4);
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }

        /* Right side user info */
        .admin-user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-shrink: 0;
            margin-left: auto;
            min-width: 0;
        }

        .admin-user-info span {
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.7rem;
            padding: 0.3rem 0.5rem;
            background: rgba(255, 215, 0, 0.05);
            border-radius: 0.5rem;
            border: 1px solid rgba(255, 215, 0, 0.2);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 120px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .admin-navbar .container {
                gap: 0.5rem;
            }

            .admin-nav-links {
                gap: 0.5rem;
            }

            .admin-nav-link {
                padding: 0.3rem 0.5rem;
                font-size: 0.7rem;
            }

            .admin-user-info span {
                font-size: 0.65rem;
                padding: 0.25rem 0.4rem;
                max-width: 100px;
            }
        }

        @media (max-width: 768px) {
            .admin-navbar {
                padding: 0.75rem 0;
            }

            .admin-navbar .container {
                padding: 0 1rem;
                gap: 1rem;
            }

            .admin-logo img {
                height: 45px;
            }

            .admin-nav-links {
                gap: 0.5rem;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .admin-nav-links::-webkit-scrollbar {
                display: none;
            }

            .admin-nav-link {
                padding: 0.4rem 0.6rem;
                font-size: 0.75rem;
            }

            .admin-user-info span {
                font-size: 0.7rem;
                padding: 0.3rem 0.6rem;
            }

            .btn {
                font-size: 0.8rem;
                padding: 0.5rem 1rem;
            }

            .terminal-window {
                margin: 1rem 0;
            }

            h1 {
                font-size: 1.75rem;
            }
        }

        @media (max-width: 480px) {
            .admin-navbar .container {
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
            }

            .admin-logo {
                order: 1;
                width: 100%;
                justify-content: center;
            }

            .admin-logo img {
                height: 40px;
            }

            .admin-nav-links {
                order: 2;
                flex: none;
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .admin-user-info {
                order: 3;
                margin-left: 0;
                justify-content: center;
                gap: 0.5rem;
            }

            .admin-nav-link {
                padding: 0.3rem 0.5rem;
                font-size: 0.7rem;
            }

            .btn {
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }

            .terminal-content {
                padding: 1rem;
            }
        }

        /* Main Content */
        .admin-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: calc(100vh - 120px);
        }

        /* Terminal Window Styles - Match Main Site */
        .terminal-window {
            background: var(--terminal-bg);
            border: 1px solid var(--terminal-border);
            border-radius: 8px;
            box-shadow: var(--shadow-terminal);
            overflow: hidden;
            font-family: 'Fira Code', 'JetBrains Mono', monospace;
            position: relative;
            margin-bottom: 2rem;
        }

        .terminal-window:last-child {
            margin-bottom: 0;
        }

        /* Terminal Window Variants */
        .terminal-window.terminal-xl {
            margin-bottom: 3rem;
        }

        .terminal-window.terminal-sm {
            margin-bottom: 1rem;
        }

        .terminal-header {
            background: var(--terminal-header);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 1px solid var(--terminal-border);
        }

        .terminal-dots {
            display: flex;
            gap: 6px;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .terminal-dot.red { background: var(--terminal-dot-red); }
        .terminal-dot.yellow { background: var(--terminal-dot-yellow); }
        .terminal-dot.green { background: var(--terminal-dot-green); }

        .terminal-title {
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .terminal-content {
            padding: 1.5rem;
        }

        /* Cyber Button System - Match Main Site */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: var(--dark);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover), var(--primary));
            box-shadow: var(--glow-primary);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary);
            border-color: var(--primary);
        }

        .btn-secondary:hover {
            background: var(--primary);
            color: var(--dark);
            box-shadow: var(--glow-primary);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--error), #CC0033);
            color: white;
            border-color: var(--error);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #ff4444, var(--error));
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
            transform: translateY(-2px);
        }

        .btn-terminal {
            background: var(--terminal-bg);
            color: var(--text-terminal);
            border-color: var(--terminal-border);
            font-family: 'Fira Code', monospace;
        }

        .btn-terminal:hover {
            background: var(--dark-lighter);
            box-shadow: var(--glow-terminal);
            border-color: var(--text-terminal);
        }

        /* Button Groups */
        .btn-group {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-group .btn {
            margin: 0;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
        }

        /* Grid Layouts */
        .admin-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .admin-grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        }
        .admin-grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }
        .admin-grid-4 {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        }

        /* Responsive Grid Adjustments */
        @media (max-width: 1200px) {
            .admin-grid-4 {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 992px) {
            .admin-grid-3 {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
            .admin-grid-4 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .admin-grid-2,
            .admin-grid-3,
            .admin-grid-4 {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        /* Cards */
        .admin-card {
            background: var(--dark-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .admin-card h3 {
            color: var(--primary);
            margin-bottom: 1rem;
            font-family: 'Orbitron', sans-serif;
        }

        /* Tables */
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .admin-table th,
        .admin-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .admin-table th {
            background: rgba(255, 215, 0, 0.1);
            color: var(--primary);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
        }

        .admin-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Admin Layout Components */
        .admin-header {
            background: var(--dark-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .admin-header-content {
            flex: 1;
            min-width: 200px;
        }

        .admin-header-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .admin-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .admin-card-header {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }

        .admin-card-header h3 {
            margin: 0;
            color: var(--primary);
            font-family: 'Orbitron', sans-serif;
        }

        /* Statistics Components */
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .stat-item:last-child {
            border-bottom: none;
        }

        .stat-label {
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
        }

        .stat-value {
            color: var(--primary);
            font-family: 'JetBrains Mono', monospace;
            font-weight: 600;
        }

        /* Container System */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .admin-main {
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        /* Additional Layout Classes */
        .backup-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .backup-item {
            background: var(--dark-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .backup-info {
            flex: 1;
        }

        .backup-actions {
            display: flex;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        .status-success {
            background: rgba(0, 255, 65, 0.2);
            color: var(--success);
        }

        .status-warning {
            background: rgba(255, 215, 0, 0.2);
            color: var(--warning);
        }

        .status-error {
            background: rgba(255, 0, 64, 0.2);
            color: var(--error);
        }

        /* Visual Polish */
        .admin-card {
            transition: all 0.3s ease;
        }

        .admin-card:hover {
            transform: translateY(-2px);
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }

        .terminal-window {
            transition: all 0.3s ease;
        }

        .terminal-window:hover {
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.7);
        }

        /* Smooth Animations */
        * {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Focus States */
        .btn:focus,
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-main {
                padding: 1rem;
            }

            .admin-navbar .container {
                padding: 0 1rem;
            }

            .admin-nav-links {
                gap: 1rem;
            }

            .admin-header {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .admin-header-actions {
                justify-content: center;
            }

            .admin-grid-2,
            .admin-grid-3,
            .admin-grid-4 {
                grid-template-columns: 1fr;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="cyber-grid-bg"></div>

    <!-- Admin Navigation -->
    <nav class="admin-navbar">
        <div class="container">
            <div class="admin-logo">
                <a href="{% url 'home' %}">
                    <img src="/static/assets/HeaderLogo.png" alt="KERNELiOS">
                </a>
            </div>
            <div class="admin-nav-links">
                <a href="{% url 'admin_panel:admin_dashboard' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_dashboard' %}active{% endif %}">
                    🏠 Dashboard
                </a>
                <a href="{% url 'admin_panel:admin_test_versions' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_test_versions' %}active{% endif %}">
                    🧬 Scenarios
                </a>
                <a href="{% url 'admin_panel:admin_instances' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_instances' %}active{% endif %}">
                    🎯 Instances
                </a>
                <a href="{% url 'admin_panel:admin_users' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_users' %}active{% endif %}">
                    👥 Users
                </a>
                <a href="{% url 'admin_panel:admin_analytics_dashboard' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_analytics_dashboard' %}active{% endif %}">
                    📊 Analytics
                </a>
                <a href="{% url 'admin_panel:admin_backup_management' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_backup_management' %}active{% endif %}">
                    💾 Backups
                </a>
                <a href="{% url 'admin_panel:admin_monitoring_dashboard' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_monitoring_dashboard' %}active{% endif %}">
                    📊 Monitoring
                </a>
                <a href="{% url 'admin_panel:admin_settings' %}" class="admin-nav-link {% if request.resolver_match.url_name == 'admin_settings' %}active{% endif %}">
                    ⚙️ Settings
                </a>
            </div>

            <div class="admin-user-info">
                <span>
                    root@{{ user.username }}:~#
                </span>
                <a href="{% url 'logout' %}" class="admin-nav-link" style="background: rgba(255, 61, 0, 0.1); border-color: rgba(255, 61, 0, 0.3); color: #ff6b35;">
                    🚪 Logout
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        {% block content %}{% endblock %}
    </main>

    {% block extra_js %}{% endblock %}
</body>
</html>
