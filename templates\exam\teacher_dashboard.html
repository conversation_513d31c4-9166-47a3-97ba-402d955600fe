{% extends 'exam/base.html' %}

{% block title %}Teacher Dashboard - KERNELiOS{% endblock %}

{% block content %}
{% csrf_token %}
<div class="teacher-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header">
        <div class="terminal-window">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">teacher_dashboard.py</div>
            </div>
            <div class="terminal-content">
                <div class="header-content">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <h1 class="dashboard-title">👨‍🏫 Teacher Dashboard</h1>
                            <p class="dashboard-subtitle">Simulation Management & Monitoring System</p>
                        </div>
                        {% if user.is_superuser %}
                        <div>
                            <a href="/admin-panel/dashboard/" class="btn btn-primary" style="margin-right: 1rem;">
                                🛡️ Admin Panel
                            </a>
                            <a href="/admin-panel/test-versions/" class="btn btn-secondary">
                                📚 Manage Test Versions
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instance Management Terminal -->
    <div class="management-section">
        <div class="terminal-window">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">instance_manager.sh</div>
            </div>
            <div class="terminal-content">
                <div class="section-header">
                    <h2>📚 Exam Instance Management</h2>
                </div>

                <div class="instance-controls">
                    <div class="control-group">
                        <label class="form-label">🎯 Active Instance:</label>
                        <div class="input-container">
                            <span class="input-prompt">></span>
                            <select id="instanceSelector" onchange="switchInstance()" class="form-input">
                                <option value="">Select exam instance...</option>
                                {% for instance in all_instances %}
                                    <option value="{{ instance.id }}" {% if instance == selected_instance %}selected{% endif %}>
                                        {{ instance.name }} ({{ instance.version.name }}) - {{ instance.student_count }} students
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <button onclick="showCreateInstanceModal()" class="btn btn-primary">
                        ➕ Create New Instance
                    </button>
                </div>

                {% if selected_instance %}
                <div class="instance-details">
                    <div class="detail-header">
                        <h3>📊 Instance Status: {{ selected_instance.name }}</h3>
                    </div>
                    <div class="detail-content">
                        <div class="terminal-line">├─ Description: <span style="color: var(--text-secondary);">{{ selected_instance.description }}</span></div>
                        <div class="terminal-line">├─ Version: <span style="color: var(--primary);">{{ selected_instance.version.name }}</span></div>
                        <div class="terminal-line">├─ Students: <span style="color: var(--success);">{{ selected_instance.student_count }}</span></div>
                        <div class="terminal-line">├─ Registration:
                            {% if selected_instance.registration_open %}
                                <span style="color: var(--success);">✓ OPEN</span>
                            {% else %}
                                <span style="color: var(--error);">✗ CLOSED</span>
                            {% endif %}
                        </div>
                        <div class="terminal-line">└─ Created: <span style="color: var(--text-terminal-secondary);">{{ selected_instance.created_at|date:"M d, Y H:i" }}</span></div>
                    </div>

                    <div class="instance-actions">
                        <button onclick="toggleRegistration({{ selected_instance.id }})" class="btn {% if selected_instance.registration_open %}btn-warning{% else %}btn-success{% endif %}">
                            {% if selected_instance.registration_open %}🔒 Close Registration{% else %}🔓 Open Registration{% endif %}
                        </button>

                        <button onclick="toggleInstancePause({{ selected_instance.id }})" class="btn {% if selected_instance.exam_paused %}btn-success{% else %}btn-warning{% endif %}">
                            {% if selected_instance.exam_paused %}▶️ Resume Exam{% else %}⏸️ Pause Exam{% endif %}
                        </button>

                        <a href="{% url 'instance_questions' selected_instance.id %}" class="btn btn-secondary">
                            📝 View Questions
                        </a>

                        <a href="{% url 'export_instance_csv' selected_instance.id %}" class="btn btn-terminal">
                            📊 Export CSV
                        </a>

                        <button onclick="emailExport({{ selected_instance.id }}, '{{ selected_instance.name }}')" class="btn btn-primary">
                            📧 Email Export
                        </button>

                        <button onclick="endInstanceTest({{ selected_instance.id }}, '{{ selected_instance.name }}')" class="btn btn-danger">
                            🛑 End Test
                        </button>

                        <button onclick="deleteInstance({{ selected_instance.id }}, '{{ selected_instance.name }}', {{ selected_instance.can_be_deleted|yesno:'true,false' }})"
                                class="btn {% if selected_instance.can_be_deleted %}btn-danger{% else %}btn-disabled{% endif %}"
                                {% if not selected_instance.can_be_deleted %}disabled title="Export data first to enable deletion"{% endif %}>
                            🗑️ Delete Instance
                        </button>
                    </div>
                </div>
                {% else %}
                <div class="no-instance">
                    <div class="terminal-line">📋 No instances available</div>
                    <div class="terminal-line">└─ Create your first instance to get started!</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Message Display -->
    <div id="controlMessage" class="control-message"></div>

    <!-- System Stats Terminal -->
    <div class="stats-section">
        <div class="terminal-window">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">system_stats.py</div>
            </div>
            <div class="terminal-content">
                <div class="section-header">
                    <h2>📊 System Overview</h2>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <div class="stat-value">{{ total_students }}</div>
                            <div class="stat-label">Total Students</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">🟢</div>
                        <div class="stat-content">
                            <div class="stat-value">{{ active_students }}</div>
                            <div class="stat-label">Active Students</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">{% if app_config.exam_paused %}⏸️{% else %}▶️{% endif %}</div>
                        <div class="stat-content">
                            <div class="stat-value" style="color: {% if app_config.exam_paused %}var(--warning){% else %}var(--success){% endif %};">
                                {% if app_config.exam_paused %}PAUSED{% else %}ACTIVE{% endif %}
                            </div>
                            <div class="stat-label">Exam Status</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <div class="stat-value">{{ all_instances.count }}</div>
                            <div class="stat-label">Total Instances</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    
    <!-- Student Progress -->
    <div class="card">
        <h3 style="color: var(--primary);">Student Progress</h3>
        
        {% if student_progress %}
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="border-bottom: 2px solid var(--gray-750);">
                            <th style="padding: 1rem; text-align: left; color: var(--primary);">Student</th>
                            <th style="padding: 1rem; text-align: left; color: var(--primary);">Instance</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Progress</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Score</th>
                            <th style="padding: 1rem; text-align: left; color: var(--primary);">Current Question</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Status</th>
                            <th style="padding: 1rem; text-align: center; color: var(--primary);">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for progress in student_progress %}
                            <tr style="border-bottom: 1px solid var(--gray-850);">
                                <td style="padding: 1rem;">{{ progress.player.user.username }}</td>
                                <td style="padding: 1rem;">
                                    <div>{{ progress.player.instance.name }}</div>
                                    <small style="color: var(--text-muted);">{{ progress.player.version.name }}</small>
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    <div style="background-color: var(--dark-card); border-radius: 1rem; padding: 0.25rem;">
                                        <div style="background-color: var(--primary); height: 0.5rem; border-radius: 1rem; 
                                                    width: {{ progress.progress_percentage }}%; transition: width 0.3s ease;"></div>
                                    </div>
                                    <small style="color: var(--text-secondary);">
                                        {{ progress.completed_questions }}/{{ progress.total_questions }}
                                    </small>
                                </td>
                                <td style="padding: 1rem; text-align: center; font-weight: bold;">
                                    {{ progress.player.score|floatformat:1 }}
                                </td>
                                <td style="padding: 1rem;">{{ progress.current_level }}</td>
                                <td style="padding: 1rem; text-align: center;">
                                    {% if progress.player.end_time %}
                                        <span style="color: #22c55e;">✓ Complete</span>
                                    {% elif progress.player.start_time %}
                                        <span style="color: var(--primary);">📝 In Progress</span>
                                    {% else %}
                                        <span style="color: var(--text-muted);">⏳ Not Started</span>
                                    {% endif %}
                                </td>
                                <td style="padding: 1rem; text-align: center;">
                                    <button onclick="resetPassword({{ progress.player.user.id }}, '{{ progress.player.user.username }}')" 
                                            class="btn" style="background-color: var(--secondary); color: white; font-size: 0.8rem; padding: 0.5rem 1rem;">
                                        Reset Password
                                    </button>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <p style="text-align: center; color: var(--text-muted); padding: 2rem;">
                No students registered yet.
            </p>
        {% endif %}
    </div>
</div>

<!-- Password Reset Modal -->
<div id="passwordModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                                background-color: rgba(0,0,0,0.8); z-index: 2000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                background-color: var(--dark-lighter); padding: 2rem; border-radius: 1rem; 
                max-width: 400px; width: 90%;">
        <h3 style="color: var(--primary);">Reset Password</h3>
        <p id="resetUsername" style="margin-bottom: 1rem;"></p>
        <form id="passwordForm">
            {% csrf_token %}
            <input type="hidden" id="resetUserId">
            <input type="password" id="newPassword" class="form-input" placeholder="New Password" required>
            <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                <button type="submit" class="btn btn-primary">Reset Password</button>
                <button type="button" onclick="closePasswordModal()" class="btn" 
                        style="background-color: var(--gray-750); color: var(--text-primary);">Cancel</button>
            </div>
        </form>
    </div>
</div>

<script>
let examPaused = {{ app_config.exam_paused|yesno:"true,false" }};

function toggleExamPause() {
    const action = examPaused ? 'resume' : 'pause';
    
    fetch('/teacher/pause-exam/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `action=${action}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            examPaused = data.paused;
            const btn = document.getElementById('pauseBtn');
            btn.textContent = examPaused ? 'Resume Exam' : 'Pause Exam';
            btn.style.backgroundColor = examPaused ? '#22c55e' : 'var(--accent)';
            showMessage(data.message, 'success');
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to toggle exam state', 'error');
    });
}



function resetPassword(userId, username) {
    document.getElementById('resetUserId').value = userId;
    document.getElementById('resetUsername').textContent = `Reset password for: ${username}`;
    document.getElementById('passwordModal').style.display = 'block';
}

function closePasswordModal() {
    document.getElementById('passwordModal').style.display = 'none';
    document.getElementById('newPassword').value = '';
}

document.getElementById('passwordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const userId = document.getElementById('resetUserId').value;
    const newPassword = document.getElementById('newPassword').value;
    
    fetch(`/teacher/reset-password/${userId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `new_password=${encodeURIComponent(newPassword)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            closePasswordModal();
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to reset password', 'error');
    });
});

// Instance Management Functions
function switchInstance() {
    const selector = document.getElementById('instanceSelector');
    const instanceId = selector.value;

    if (instanceId) {
        window.location.href = `?instance=${instanceId}`;
    } else {
        window.location.href = window.location.pathname;
    }
}

function showCreateInstanceModal() {
    document.getElementById('createInstanceModal').style.display = 'block';
}

function closeCreateInstanceModal() {
    document.getElementById('createInstanceModal').style.display = 'none';
    document.getElementById('createInstanceForm').reset();
}

function createInstance() {
    const form = document.getElementById('createInstanceForm');
    const formData = new FormData(form);

    fetch('{% url "create_instance" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            closeCreateInstanceModal();
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to create instance', 'error');
    });
}

function toggleRegistration(instanceId) {
    fetch(`/teacher/toggle-registration/${instanceId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to toggle registration', 'error');
    });
}

function toggleInstancePause(instanceId) {
    // Determine current state and action
    const button = event.target;
    const isPaused = button.textContent.includes('Resume');
    const action = isPaused ? 'resume' : 'pause';
    const originalText = button.textContent;
    const originalColor = button.style.backgroundColor;

    // Show loading state
    button.textContent = '⏳ Processing...';
    button.disabled = true;

    fetch('/teacher/pause-exam/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `action=${action}&instance_id=${instanceId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');

            // Update button immediately
            if (data.paused) {
                button.textContent = '▶️ Resume Exam';
                button.style.backgroundColor = '#22c55e';
            } else {
                button.textContent = '⏸️ Pause Exam';
                button.style.backgroundColor = 'var(--accent)';
            }

            // Notify students immediately if paused
            if (data.paused) {
                notifyStudentsOfPause(instanceId);
            }
        } else {
            showMessage(data.error, 'error');
            // Restore original state on error
            button.textContent = originalText;
            button.style.backgroundColor = originalColor;
        }
        button.disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to toggle pause', 'error');
        // Restore original state on error
        button.textContent = originalText;
        button.style.backgroundColor = originalColor;
        button.disabled = false;
    });
}



function emailExport(instanceId, instanceName) {
    const email = prompt(`📧 Email CSV Export for "${instanceName}"\n\nEnter email address to send the export to:`);
    if (!email) return;

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        alert('Please enter a valid email address');
        return;
    }

    if (confirm(`Send CSV export for "${instanceName}" to ${email}?`)) {
        window.location.href = `/teacher/export-instance/${instanceId}/?email=${encodeURIComponent(email)}`;
    }
}

function endInstanceTest(instanceId, instanceName) {
    if (!confirm(`🛑 END TEST for "${instanceName}"?\n\nThis will:\n- End the test for ALL students in this instance\n- Pause the instance\n- Students will no longer be able to submit answers\n\nThis action cannot be undone!`)) {
        return;
    }

    fetch('/teacher/end-test/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `instance_id=${instanceId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to end test', 'error');
    });
}

function deleteInstance(instanceId, instanceName, canDelete) {
    if (!canDelete) {
        alert('⚠️ Cannot delete this instance!\n\nThis instance has students but has not been exported yet.\nPlease export the data first using the "📊 Export CSV" button.');
        return;
    }

    if (!confirm(`⚠️ Are you sure you want to delete the instance "${instanceName}"?\n\nThis will permanently delete:\n- The instance\n- All registered students\n- All their progress and scores\n\nThis action cannot be undone!`)) {
        return;
    }

    fetch(`/teacher/delete-instance/${instanceId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            setTimeout(() => window.location.href = '/teacher/', 2000);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to delete instance', 'error');
    });
}

function notifyStudentsOfPause(instanceId) {
    // This function could be enhanced with WebSockets for real-time notifications
    // For now, students will detect pause on their next action or page refresh
    console.log(`Instance ${instanceId} has been paused - students will be notified on next interaction`);
}

function showMessage(message, type) {
    const messageDiv = document.getElementById('controlMessage');
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    messageDiv.style.backgroundColor = type === 'success' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)';
    messageDiv.style.borderColor = type === 'success' ? 'rgba(34, 197, 94, 0.3)' : 'rgba(239, 68, 68, 0.3)';
    messageDiv.style.color = type === 'success' ? '#22c55e' : '#ef4444';
    
    setTimeout(() => {
        messageDiv.style.display = 'none';
    }, 5000);
}

// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>

<!-- Create Instance Modal -->
<div id="createInstanceModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                    background-color: rgba(0,0,0,0.8); z-index: 2000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background-color: var(--dark-lighter); padding: 2rem; border-radius: 1rem;
                max-width: 500px; width: 90%; position: relative;">

        <!-- Close X Button -->
        <button onclick="closeCreateInstanceModal()"
                style="position: absolute; top: 1rem; right: 1rem; background: none; border: none;
                       color: var(--text-muted); font-size: 1.5rem; cursor: pointer;
                       width: 30px; height: 30px; display: flex; align-items: center; justify-content: center;
                       border-radius: 50%; transition: all 0.3s ease;"
                onmouseover="this.style.backgroundColor='var(--gray-750)'; this.style.color='var(--text-primary)';"
                onmouseout="this.style.backgroundColor='transparent'; this.style.color='var(--text-muted)';">
            ✕
        </button>

        <h3 style="color: var(--primary); margin-right: 2rem;">Create New Exam Instance</h3>

        <form id="createInstanceForm" onsubmit="event.preventDefault(); createInstance();">
            {% csrf_token %}

            <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Instance Name:</label>
                <input type="text" name="name" class="form-input" placeholder="e.g., Class A Morning Session" required>
            </div>

            <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Description (Optional):</label>
                <textarea name="description" class="form-input" rows="3" placeholder="Brief description of this exam session..."></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Exam Version:</label>
                <select name="version_id" class="form-input" required>
                    <option value="">Select exam version...</option>
                    {% for version in all_versions %}
                        <option value="{{ version.id }}">{{ version.name }} - {{ version.description }}</option>
                    {% endfor %}
                </select>
            </div>

            <div style="display: flex; gap: 1rem;">
                <button type="submit" class="btn btn-primary">Create Instance</button>
                <button type="button" onclick="closeCreateInstanceModal()" class="btn"
                        style="background-color: var(--gray-750); color: var(--text-primary);">Cancel</button>
            </div>
        </form>
    </div>
</div>

<style>
/* Teacher Dashboard Styles */
.teacher-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.header-content {
    text-align: center;
    padding: 2rem 0;
}

.dashboard-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
}

.management-section {
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--terminal-border);
}

.section-header h2 {
    color: var(--primary);
    font-family: 'Orbitron', sans-serif;
    margin: 0;
    font-size: 1.5rem;
}

.instance-controls {
    display: flex;
    gap: 2rem;
    align-items: flex-end;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.control-group {
    flex: 1;
    min-width: 300px;
}

.instance-details {
    background: var(--dark-card);
    border: 1px solid var(--terminal-border);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.detail-header h3 {
    color: var(--primary);
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.detail-content {
    font-family: 'Fira Code', monospace;
    font-size: 0.875rem;
}

.instance-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.no-instance {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
    font-family: 'Fira Code', monospace;
}

.control-message {
    display: none;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid;
    font-family: 'Fira Code', monospace;
}

.stats-section {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 215, 0, 0.05);
    border: 1px solid var(--terminal-border);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: var(--primary);
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    font-family: 'Fira Code', monospace;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Button Variants */
.btn-success {
    background: linear-gradient(135deg, var(--success), #16a34a);
    color: white;
    border-color: var(--success);
}

.btn-success:hover {
    background: linear-gradient(135deg, #16a34a, var(--success));
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning), #f59e0b);
    color: white;
    border-color: var(--warning);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f59e0b, var(--warning));
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: white;
    border-color: var(--error);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, var(--error));
    box-shadow: 0 0 20px rgba(255, 0, 64, 0.3);
}

.btn-disabled {
    background: var(--gray-750);
    color: var(--text-muted);
    border-color: var(--gray-750);
    cursor: not-allowed;
}

.btn-disabled:hover {
    background: var(--gray-750);
    transform: none;
    box-shadow: none;
}

/* Terminal Lines */
.terminal-line {
    margin: 0.25rem 0;
    color: var(--text-terminal);
    font-size: 0.875rem;
    line-height: 1.4;
}
</style>

{% endblock %}
