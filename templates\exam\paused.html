{% extends 'exam/base.html' %}

{% block title %}Exam Paused - KERNELiOS{% endblock %}

{% block content %}
<div class="paused-overlay">
    <div class="paused-container">
        <!-- Main Paused Terminal -->
        <div class="terminal-window terminal-xl">
            <div class="terminal-header">
                <div class="terminal-dots">
                    <div class="terminal-dot red"></div>
                    <div class="terminal-dot yellow"></div>
                    <div class="terminal-dot green"></div>
                </div>
                <div class="terminal-title">exam_status.py</div>
            </div>
            <div class="terminal-content">
                <!-- Logo -->
                <div class="paused-logo">
                    <img src="/static/assets/logo.png" alt="KERNELiOS" style="width: 100px; height: 100px; filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.4));">
                </div>

                <!-- Status Display -->
                <div class="status-display">
                    <h1 class="paused-title">⏸️ EXAM PAUSED</h1>

                    <div class="status-info">
                        <div class="terminal-line">📊 System Status: <span style="color: var(--warning);">PAUSED</span></div>
                        <div class="terminal-line">🎯 Reason:
                            {% if pause_type == 'global' %}
                                <span style="color: var(--accent);">Global pause by administrator</span>
                            {% else %}
                                <span style="color: var(--accent);">Instance "{{ instance_name }}" paused by instructor</span>
                            {% endif %}
                        </div>
                        <div class="terminal-line">⏱️ Timer Status: <span style="color: var(--primary);">SUSPENDED</span></div>
                        <div class="terminal-line">🔄 Auto-refresh: <span style="color: var(--success);">ACTIVE</span></div>
                    </div>

                    <div class="pause-notice">
                        <div class="notice-header">⏱️ Timer Suspended</div>
                        <div class="notice-text">Your examination timer has been paused and will resume when the exam is unpaused.</div>
                    </div>

                    <div class="wait-message">
                        <div class="terminal-line">📢 Please wait for further instructions...</div>
                        <div class="terminal-line">🔄 Checking for resume signal...</div>
                        <div style="margin-top: 1rem;">
                            <span class="terminal-cursor blinking"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Indicator -->
        <div class="status-indicator">
            <div class="pulse-dot"></div>
        </div>
    </div>
</div>

<style>
/* Paused Page Styles */
.paused-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--dark) 0%, var(--dark-lighter) 50%, var(--dark) 100%);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.paused-container {
    max-width: 600px;
    width: 100%;
    text-align: center;
}

.paused-logo {
    margin-bottom: 2rem;
}

.status-display {
    padding: 1rem;
}

.paused-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 2.5rem;
    color: var(--warning);
    margin-bottom: 2rem;
    text-shadow: 0 0 20px rgba(255, 165, 0, 0.3);
}

.status-info {
    margin: 2rem 0;
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    text-align: left;
}

.pause-notice {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid var(--primary);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
}

.notice-header {
    color: var(--primary);
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.notice-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.wait-message {
    margin-top: 2rem;
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    text-align: left;
}

.status-indicator {
    margin-top: 3rem;
    display: flex;
    justify-content: center;
}

.pulse-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary);
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.blinking {
    animation: blink 1s infinite;
}

.terminal-line {
    margin: 0.5rem 0;
    color: var(--text-terminal);
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
    }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .paused-title {
        font-size: 2rem;
    }

    .paused-overlay {
        padding: 1rem;
    }

    .status-info,
    .wait-message {
        font-size: 0.8rem;
    }
}
</style>

<script>
// Auto-refresh every 10 seconds to check if simulation is resumed
setTimeout(function() {
    location.reload();
}, 10000);

// Pause any running timers on the page
if (typeof window.examTimer !== 'undefined') {
    clearInterval(window.examTimer);
}
if (typeof window.questionTimer !== 'undefined') {
    clearInterval(window.questionTimer);
}
</script>
{% endblock %}
