<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KERNELiOS - Advanced Simulator System{% endblock %}</title>
    <link rel="icon" type="image/png" href="/static/assets/logo.png">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600;700&family=Orbitron:wght@500;600;700&family=Fira+Code:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* KernelIOS Cybersecurity Terminal UI System */
        :root {
            /* Primary Colors - Cyber Gold */
            --primary: #FFD700;
            --primary-light: #FFEB3B;
            --primary-dark: #FFC107;
            --primary-hover: #FFEE58;

            /* Secondary Colors - Deep Purple */
            --secondary: #4B0082;
            --secondary-light: #6A0DAD;
            --secondary-dark: #2E004D;
            --secondary-hover: #5D00A0;

            /* Dark Theme Base */
            --dark: #0A0A0A;
            --dark-lighter: #121212;
            --dark-light: #1A1A1A;
            --dark-card: #161616;
            --dark-terminal: #0D1117;
            --dark-terminal-header: #1C2128;

            /* Gray Scale */
            --gray-750: #3F3F46;
            --gray-850: #27272A;
            --gray-900: #1A1A1A;

            /* Accent Colors */
            --accent: #FF3D00;
            --accent-light: #FF5722;
            --success: #00FF41;
            --warning: #FFA500;
            --error: #FF0040;

            /* Text Colors */
            --text-primary: #FFFFFF;
            --text-secondary: #CCCCCC;
            --text-muted: #999999;
            --text-terminal: #00FF41;
            --text-terminal-secondary: #FFD700;

            /* Terminal Colors */
            --terminal-bg: #0D1117;
            --terminal-border: #30363D;
            --terminal-header: #1C2128;
            --terminal-dot-red: #FF5F56;
            --terminal-dot-yellow: #FFBD2E;
            --terminal-dot-green: #27CA3F;

            /* Shadows and Effects */
            --shadow-terminal: 0 8px 32px rgba(0, 0, 0, 0.6);
            --glow-primary: 0 0 20px rgba(255, 215, 0, 0.3);
            --glow-terminal: 0 0 15px rgba(0, 255, 65, 0.2);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, var(--dark-lighter) 50%, var(--dark) 100%);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
        }

        /* Cyber Background Animation */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 61, 0, 0.02) 0%, transparent 50%);
            z-index: -2;
            animation: cyberPulse 8s ease-in-out infinite alternate;
        }

        @keyframes cyberPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.7; }
        }
        
        h1, h2, h3, h4 {
            font-family: 'Orbitron', sans-serif;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2.5rem;
            background: linear-gradient(to right, var(--primary), var(--primary-light));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1.5rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        /* Cyber Grid Background */
        .cyber-grid-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                linear-gradient(rgba(255, 215, 0, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 215, 0, 0.03) 1px, transparent 1px);
            background-size: 40px 40px;
            z-index: -1;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(40px, 40px); }
        }

        /* Terminal Window Component */
        .terminal-window {
            background: var(--terminal-bg);
            border: 1px solid var(--terminal-border);
            border-radius: 8px;
            box-shadow: var(--shadow-terminal);
            overflow: hidden;
            font-family: 'Fira Code', 'JetBrains Mono', monospace;
            position: relative;
        }

        .terminal-header {
            background: var(--terminal-header);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 1px solid var(--terminal-border);
        }

        .terminal-dots {
            display: flex;
            gap: 6px;
        }

        .terminal-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .terminal-dot.red { background: var(--terminal-dot-red); }
        .terminal-dot.yellow { background: var(--terminal-dot-yellow); }
        .terminal-dot.green { background: var(--terminal-dot-green); }

        .terminal-title {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 8px;
        }

        .terminal-content {
            padding: 16px;
            color: var(--text-terminal);
            font-size: 0.875rem;
            line-height: 1.5;
            min-height: 120px;
            position: relative;
        }

        .terminal-prompt {
            color: var(--text-terminal-secondary);
            font-weight: 600;
        }

        .terminal-text {
            color: var(--text-terminal);
        }

        .terminal-cursor {
            display: inline-block;
            width: 8px;
            height: 16px;
            background: var(--text-terminal);
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Terminal Sizes */
        .terminal-sm {
            max-width: 300px;
            font-size: 0.75rem;
        }

        .terminal-md {
            max-width: 400px;
        }

        .terminal-lg {
            max-width: 600px;
        }

        .terminal-xl {
            max-width: 800px;
        }

        /* Decorative Terminal (Fixed Size) */
        .terminal-decorative {
            position: fixed;
            z-index: 1;
            pointer-events: none;
            opacity: 0.8;
        }

        .terminal-decorative .terminal-content {
            min-height: 100px;
            max-height: 150px;
            overflow: hidden;
        }
        
        /* Cyber Button System */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 600;
            font-family: 'Inter', sans-serif;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            border: 2px solid transparent;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: var(--dark);
            border-color: var(--primary);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover), var(--primary));
            box-shadow: var(--glow-primary);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary);
            border-color: var(--primary);
        }

        .btn-secondary:hover {
            background: var(--primary);
            color: var(--dark);
            box-shadow: var(--glow-primary);
        }

        .btn-terminal {
            background: var(--terminal-bg);
            color: var(--text-terminal);
            border-color: var(--terminal-border);
            font-family: 'Fira Code', monospace;
        }

        .btn-terminal:hover {
            background: var(--dark-lighter);
            box-shadow: var(--glow-terminal);
            border-color: var(--text-terminal);
        }
        
        /* Cyber Form System */
        .form-input {
            width: 100%;
            padding: 0.875rem 1rem;
            background: var(--terminal-bg);
            border: 2px solid var(--terminal-border);
            border-radius: 6px;
            color: var(--text-primary);
            font-family: 'Fira Code', monospace;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1), var(--glow-primary);
            background: var(--dark-lighter);
        }

        .form-input::placeholder {
            color: var(--text-muted);
            font-style: italic;
        }

        .form-label {
            display: block;
            color: var(--text-terminal-secondary);
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        /* Cyber Card System */
        .card {
            background: linear-gradient(135deg, var(--dark-lighter), var(--dark-card));
            border: 1px solid var(--terminal-border);
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: var(--shadow-terminal);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--primary));
            opacity: 0.6;
        }

        .card-terminal {
            background: var(--terminal-bg);
            border: 2px solid var(--terminal-border);
            border-radius: 8px;
            padding: 0;
            box-shadow: var(--shadow-terminal);
        }

        .card-cyber {
            background: linear-gradient(135deg,
                rgba(255, 215, 0, 0.05) 0%,
                var(--dark-lighter) 50%,
                rgba(0, 255, 65, 0.03) 100%);
            border: 1px solid var(--primary);
            box-shadow: var(--glow-primary);
        }
        
        /* Cyber Navigation System */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            padding: 1rem 0;
            background: linear-gradient(135deg,
                rgba(10, 10, 10, 0.95) 0%,
                rgba(18, 18, 18, 0.95) 50%,
                rgba(10, 10, 10, 0.95) 100%);
            backdrop-filter: blur(15px);
            border-bottom: 2px solid var(--terminal-border);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .navbar::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent,
                var(--primary),
                var(--primary-light),
                var(--primary),
                transparent);
            opacity: 0.6;
        }

        .navbar-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem 0 0.5rem;
            display: flex;
            align-items: center;
            gap: 3rem;
        }

        .logo {
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .logo:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        }

        .logo-img {
            height: 60px;
            width: auto;
            filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.2));
        }

        .logo-text {
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 1.75rem;
            background: linear-gradient(135deg, var(--primary), var(--primary-light), var(--primary));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex: 1;
            justify-content: center;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            padding: 0.6rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.85rem;
            font-weight: 500;
            border: 1px solid transparent;
            white-space: nowrap;
        }

        .nav-link:hover {
            color: var(--primary);
            background: rgba(255, 215, 0, 0.1);
            border-color: rgba(255, 215, 0, 0.3);
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.2);
            transform: translateY(-1px);
        }

        .nav-link.teacher {
            color: var(--accent);
            border-color: rgba(255, 61, 0, 0.3);
        }

        .nav-link.teacher:hover {
            background: rgba(255, 61, 0, 0.1);
            border-color: rgba(255, 61, 0, 0.4);
            box-shadow: 0 0 15px rgba(255, 61, 0, 0.2);
        }

        /* Right side user info */
        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-shrink: 0;
            margin-left: auto;
        }

        .user-info {
            color: var(--text-muted);
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            background: rgba(255, 215, 0, 0.05);
            border-radius: 0.5rem;
            border: 1px solid rgba(255, 215, 0, 0.2);
            white-space: nowrap;
        }
        
        /* Main Content System */
        .main-content {
            margin-top: 100px;
            min-height: calc(100vh - 100px);
            padding: 2rem 0;
            position: relative;
        }

        /* Loading Animation */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--dark), var(--dark-lighter));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-container {
            text-align: center;
            position: relative;
        }

        .loading-logo-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }

        .loading-subtitle {
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            letter-spacing: 1px;
        }

        .loading-status {
            font-family: 'Fira Code', monospace;
            font-size: 0.875rem;
            color: var(--text-terminal);
            margin-bottom: 1rem;
            animation: statusBlink 1.5s ease-in-out infinite;
        }

        @keyframes statusBlink {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .loading-dots {
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            gap: 0.5rem;
        }

        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary);
            animation: dotPulse 1.5s ease-in-out infinite;
        }

        .dot:nth-child(1) { animation-delay: 0s; }
        .dot:nth-child(2) { animation-delay: 0.3s; }
        .dot:nth-child(3) { animation-delay: 0.6s; }

        @keyframes dotPulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .loading-logo {
            width: 120px;
            height: 120px;
            margin-bottom: 2rem;
            animation: logoFloat 3s ease-in-out infinite;
            filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.6));
            position: relative;
        }

        .loading-logo::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: logoSpin 2s linear infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-10px) scale(1.05); }
        }

        @keyframes logoSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-family: 'Orbitron', sans-serif;
            font-size: 1.75rem;
            color: var(--primary);
            margin-bottom: 1.5rem;
            animation: textGlow 2s ease-in-out infinite;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            letter-spacing: 2px;
        }

        @keyframes textGlow {
            0%, 100% {
                opacity: 0.8;
                text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            }
            50% {
                opacity: 1;
                text-shadow: 0 0 30px rgba(255, 215, 0, 0.6);
            }
        }

        .loading-bar {
            width: 350px;
            height: 6px;
            background: var(--dark-lighter);
            border-radius: 3px;
            overflow: hidden;
            position: relative;
            border: 1px solid var(--terminal-border);
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg,
                var(--primary),
                var(--primary-light),
                var(--text-terminal),
                var(--primary-light),
                var(--primary));
            background-size: 200% 100%;
            border-radius: 3px;
            animation: loadingProgress 2.5s ease-in-out infinite,
                       gradientShift 1.5s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% { width: 0%; transform: translateX(-100%); }
            50% { width: 100%; transform: translateX(0%); }
            100% { width: 100%; transform: translateX(100%); }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .messages {
            margin: 1rem 0;
        }
        
        .message {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .message.success {
            background-color: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }
        
        .message.error {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .navbar-container {
                gap: 2rem;
            }

            .nav-links {
                gap: 1rem;
            }

            .nav-link {
                padding: 0.5rem 0.8rem;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 0.75rem 0;
            }

            .navbar-container {
                padding: 0 1rem;
                gap: 1rem;
            }

            .logo-img {
                height: 45px;
            }

            .nav-links {
                gap: 0.5rem;
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .nav-links::-webkit-scrollbar {
                display: none;
            }

            .nav-link {
                padding: 0.4rem 0.6rem;
                font-size: 0.75rem;
            }

            .user-section {
                gap: 0.5rem;
            }

            .user-info {
                font-size: 0.7rem;
                padding: 0.3rem 0.6rem;
            }

            .main-content {
                margin-top: 85px;
                padding: 1rem 0;
            }

            .container {
                padding: 0 0.75rem;
            }

            .terminal-window {
                margin: 1rem 0;
            }

            .terminal-decorative {
                display: none;
            }

            h1 {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .navbar-container {
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
            }

            .logo {
                order: 1;
                width: 100%;
                justify-content: center;
            }

            .logo-img {
                height: 40px;
            }

            .nav-links {
                order: 2;
                flex: none;
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .user-section {
                order: 3;
                margin-left: 0;
                justify-content: center;
                gap: 0.5rem;
            }

            .nav-link {
                padding: 0.3rem 0.5rem;
                font-size: 0.7rem;
            }

            .user-info {
                display: none;
            }

            h1 {
                font-size: 1.75rem;
            }

            .btn {
                font-size: 0.8rem;
                padding: 0.5rem 1rem;
            }

            .terminal-window {
                margin: 0.5rem 0;
            }

            .terminal-content {
                font-size: 0.75rem;
                padding: 0.75rem;
            }

            .container {
                padding: 0 0.5rem;
            }
        }

        @media (max-width: 320px) {
            .navbar {
                padding: 0.5rem 0;
            }

            .logo-img {
                height: 35px;
            }

            .nav-links {
                gap: 0.25rem;
            }

            .btn {
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }

            .terminal-content {
                font-size: 0.7rem;
                padding: 0.5rem;
            }

            h1 {
                font-size: 1.5rem;
            }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }

        .mb-1 { margin-bottom: 0.25rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-3 { margin-bottom: 0.75rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .mb-8 { margin-bottom: 2rem; }

        .mt-1 { margin-top: 0.25rem; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-3 { margin-top: 0.75rem; }
        .mt-4 { margin-top: 1rem; }
        .mt-6 { margin-top: 1.5rem; }
        .mt-8 { margin-top: 2rem; }

        .p-2 { padding: 0.5rem; }
        .p-3 { padding: 0.75rem; }
        .p-4 { padding: 1rem; }
        .p-6 { padding: 1.5rem; }
        .p-8 { padding: 2rem; }

        .flex { display: flex; }
        .flex-col { flex-direction: column; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .gap-2 { gap: 0.5rem; }
        .gap-3 { gap: 0.75rem; }
        .gap-4 { gap: 1rem; }
        .gap-6 { gap: 1.5rem; }

        .w-full { width: 100%; }
        .h-full { height: 100%; }
        .min-h-screen { min-height: 100vh; }

        .relative { position: relative; }
        .absolute { position: absolute; }
        .fixed { position: fixed; }

        .z-10 { z-index: 10; }
        .z-20 { z-index: 20; }
        .z-50 { z-index: 50; }

        .opacity-50 { opacity: 0.5; }
        .opacity-75 { opacity: 0.75; }
        .opacity-90 { opacity: 0.9; }

        .transition-all { transition: all 0.3s ease; }
        .transition-opacity { transition: opacity 0.3s ease; }

        .hover-glow:hover {
            box-shadow: var(--glow-primary);
        }

        .cyber-border {
            border: 1px solid var(--terminal-border);
            border-radius: 6px;
        }

        .cyber-glow {
            box-shadow: var(--glow-terminal);
        }
    </style>
</head>
<body>
    <div class="cyber-grid-bg"></div>
    
    <!-- Loading Screen (Main Page Only) -->
    {% if request.resolver_match.url_name == 'home' %}
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-container">
            <div class="loading-logo-container">
                <img src="/static/assets/logo.png" alt="KERNELiOS" class="loading-logo">
            </div>
            <div class="loading-text">KERNELiOS</div>
            <div class="loading-subtitle">Advanced Simulator System</div>
            <div class="loading-status" id="loadingStatus">Initializing system...</div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
            </div>
        </div>
    </div>
    {% endif %}

    <nav class="navbar">
        <div class="navbar-container">
            <div class="logo">
                <a href="{% url 'home' %}">
                    <img src="/static/assets/HeaderLogo.png" alt="KERNELiOS" class="logo-img">
                </a>
            </div>

            <div class="nav-links">
                {% if user.is_authenticated %}
                    {% if not user.is_staff %}
                        <a href="{% url 'dashboard' %}" class="nav-link">
                            🎯 Dashboard
                        </a>
                    {% endif %}
                    <a href="{% url 'scoreboard' %}" class="nav-link">
                        🏆 Leaderboard
                    </a>
                    {% if user.is_staff %}
                        <a href="{% url 'teacher_dashboard' %}" class="nav-link teacher">
                            👨‍🏫 Teacher Panel
                        </a>
                    {% endif %}
                {% endif %}
            </div>

            <div class="user-section">
                {% if user.is_authenticated %}
                    <span class="user-info">user@{{ user.username }}:~$</span>
                    <a href="{% url 'logout' %}" class="nav-link" style="background: rgba(255, 61, 0, 0.1); border-color: rgba(255, 61, 0, 0.3); color: #ff6b35;">
                        🚪 Logout
                    </a>
                {% else %}
                    <a href="{% url 'login' %}" class="nav-link">
                        🔐 Login
                    </a>
                    <a href="{% url 'register' %}" class="nav-link">
                        📝 Register
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>
    
    <main class="main-content">
        <div class="container">
            {% if messages %}
                <div class="messages">
                    {% for message in messages %}
                        <div class="message {{ message.tags }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
            
            {% block content %}
            {% endblock %}
        </div>
    </main>

    <script>
        // Loading Screen Animation
        document.addEventListener('DOMContentLoaded', function() {
            const loadingScreen = document.getElementById('loadingScreen');
            const loadingStatus = document.getElementById('loadingStatus');

            const statusMessages = [
                'Initializing system...',
                'Loading security modules...',
                'Establishing secure connection...',
                'Verifying authentication...',
                'System ready!'
            ];

            let currentStatus = 0;

            // Update status messages
            const statusInterval = setInterval(() => {
                if (currentStatus < statusMessages.length - 1) {
                    currentStatus++;
                    loadingStatus.textContent = statusMessages[currentStatus];
                } else {
                    clearInterval(statusInterval);
                }
            }, 400);

            // Hide loading screen after 2.5 seconds
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
            }, 2500);
        });

        // Terminal Animation Helper Functions
        function typeWriter(element, text, speed = 50) {
            let i = 0;
            element.innerHTML = '';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }

            type();
        }

        function createTerminalContent(lines, containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            let delay = 0;
            lines.forEach((line, index) => {
                setTimeout(() => {
                    const lineElement = document.createElement('div');
                    lineElement.innerHTML = line;
                    container.appendChild(lineElement);
                }, delay);
                delay += 800;
            });
        }

        // Cyber Background Effects
        function addCyberParticles() {
            const particleCount = 20;
            const body = document.body;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    width: 2px;
                    height: 2px;
                    background: var(--primary);
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: -1;
                    opacity: 0.6;
                    animation: float ${5 + Math.random() * 10}s linear infinite;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation-delay: ${Math.random() * 5}s;
                `;
                body.appendChild(particle);
            }
        }

        // Add floating particles
        setTimeout(addCyberParticles, 2000);

        // Add cyber scan lines effect
        function addScanLines() {
            const scanLine = document.createElement('div');
            scanLine.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 2px;
                background: linear-gradient(90deg, transparent, var(--primary), transparent);
                z-index: -1;
                opacity: 0.3;
                animation: scanMove 3s linear infinite;
                pointer-events: none;
            `;
            document.body.appendChild(scanLine);

            setTimeout(() => {
                if (scanLine.parentNode) {
                    scanLine.remove();
                }
            }, 3000);
        }

        // Add scan lines periodically
        setInterval(addScanLines, 8000);

        // Add CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float {
                0% { transform: translateY(0px) rotate(0deg); opacity: 0; }
                10% { opacity: 0.6; }
                90% { opacity: 0.6; }
                100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
            }

            @keyframes scanMove {
                0% { top: -2px; opacity: 0; }
                50% { opacity: 0.3; }
                100% { top: 100vh; opacity: 0; }
            }

            @keyframes dataFlow {
                0% { transform: translateX(-100%); opacity: 0; }
                50% { opacity: 0.6; }
                100% { transform: translateX(100vw); opacity: 0; }
            }

            /* Subtle screen flicker effect */
            @keyframes screenFlicker {
                0%, 98% { opacity: 1; }
                99% { opacity: 0.98; }
                100% { opacity: 1; }
            }

            body {
                animation: screenFlicker 4s infinite;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
