{% extends 'admin/admin_base.html' %}

{% block title %}Scenarios - KERNELiOS Control{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">scenarios.py</div>
    </div>
    <div class="terminal-content">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="font-family: 'Orbitron', sans-serif; color: var(--primary); margin-bottom: 0.5rem;">
                    🧬 Scenario Management
                </h1>
                <p style="color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                    > Create and manage simulation scenarios with advanced element building
                </p>
            </div>
            <a href="{% url 'admin_panel:admin_create_test_version' %}" class="btn btn-primary">
                ➕ Create New Scenario
            </a>
        </div>
    </div>
</div>

<!-- Scenarios List -->
<div class="terminal-window">
    <div class="terminal-header">
        <div class="terminal-dots">
            <div class="terminal-dot red"></div>
            <div class="terminal-dot yellow"></div>
            <div class="terminal-dot green"></div>
        </div>
        <div class="terminal-title">scenario_list.log</div>
    </div>
    <div class="terminal-content">
        {% if versions %}
            <div class="admin-grid admin-grid-2">
                {% for version in versions %}
                <div class="admin-card" style="border-left: 4px solid {% if version.is_active %}var(--success){% else %}var(--warning){% endif %};">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem; flex-wrap: wrap; gap: 1rem;">
                        <div style="flex: 1; min-width: 200px;">
                            <h3 style="color: var(--primary); margin-bottom: 0.5rem; font-family: 'Orbitron', sans-serif;">
                                {{ version.name }}
                            </h3>
                            <p style="color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;">
                                {{ version.description|default:"No description" }}
                            </p>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            {% if version.is_active %}
                                <span class="status-badge status-success">
                                    ACTIVE
                                </span>
                            {% else %}
                                <span class="status-badge status-warning">
                                    INACTIVE
                                </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Scenario Statistics -->
                    <div class="admin-grid admin-grid-3" style="margin-bottom: 1.5rem;">
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--success); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.normal_questions }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Standard Elements</div>
                        </div>
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--accent); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.bonus_questions }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Advanced Elements</div>
                        </div>
                        <div class="stat-item" style="flex-direction: column; text-align: center; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border-radius: 0.5rem; border-bottom: none;">
                            <div style="color: var(--warning); font-size: 1.5rem; font-weight: bold; font-family: 'JetBrains Mono', monospace;">{{ version.total_instances }}</div>
                            <div style="color: var(--text-muted); font-size: 0.8rem;">Active Instances</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div style="display: flex; gap: 0.75rem; flex-wrap: wrap;">
                        <a href="{% url 'admin_panel:admin_test_version_detail' version.id %}" class="btn btn-primary" style="flex: 1; min-width: 120px;">
                            📝 Edit Elements
                        </a>
                        <button onclick="toggleVersionStatus({{ version.id }}, {{ version.is_active|yesno:'false,true' }})"
                                class="btn {% if version.is_active %}btn-secondary{% else %}btn-primary{% endif %}"
                                style="flex: 1; min-width: 120px;">
                            {% if version.is_active %}⏸️ Deactivate{% else %}▶️ Activate{% endif %}
                        </button>
                        <button onclick="duplicateVersion({{ version.id }})" class="btn btn-secondary" style="flex: 1; min-width: 120px;">
                            📋 Duplicate
                        </button>
                    </div>
                    
                    <!-- Metadata -->
                    <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(255, 255, 255, 0.1); font-size: 0.8rem; color: var(--text-muted); font-family: 'JetBrains Mono', monospace;">
                        Created: {{ version.created_at|date:"M d, Y H:i" }}
                        {% if version.updated_at != version.created_at %}
                            | Updated: {{ version.updated_at|date:"M d, Y H:i" }}
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div style="text-align: center; padding: 4rem 2rem; color: var(--text-muted);">
                <div style="font-size: 4rem; margin-bottom: 1rem;">🧬</div>
                <h3 style="color: var(--text-secondary); margin-bottom: 1rem;">No Scenarios Found</h3>
                <p style="margin-bottom: 2rem;">Create your first scenario to get started with the simulator system.</p>
                <a href="{% url 'admin_panel:admin_create_test_version' %}" class="btn btn-primary">
                    ➕ Create First Scenario
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
{% if versions %}
<div class="admin-grid admin-grid-3">
    <div class="admin-card">
        <h3>📊 Total Scenarios</h3>
        <div style="font-size: 2rem; color: var(--primary); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {{ versions.count }}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">
            Active: {{ versions|length }}
        </p>
    </div>
    
    <div class="admin-card">
        <h3>🎯 Active Instances</h3>
        <div style="font-size: 2rem; color: var(--warning); font-family: 'JetBrains Mono', monospace; margin: 1rem 0;">
            {% with total_instances=0 %}
                {% for version in versions %}
                    {% with total_instances=total_instances|add:version.total_instances %}{% endwith %}
                {% endfor %}
                {{ total_instances }}
            {% endwith %}
        </div>
        <p style="color: var(--text-muted); font-size: 0.9rem;">Running simulations</p>
    </div>
    
    <div class="admin-card">
        <h3>⚡ Quick Actions</h3>
        <div style="display: grid; gap: 0.5rem; margin-top: 1rem;">
            <button onclick="showBulkImportModal()" class="btn btn-secondary">
                📥 Bulk Import Questions
            </button>
            <button onclick="exportAllVersions()" class="btn btn-secondary">
                📤 Export All
            </button>
        </div>
    </div>
</div>
{% endif %}

<!-- Bulk Import Modal -->
<div id="bulkImportModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 style="color: var(--primary); margin: 0;">📥 Bulk Import Questions</h3>
            <button type="button" onclick="closeBulkImportModal()" class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <div style="background: rgba(255, 215, 0, 0.1); border: 1px solid var(--primary); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <h4 style="color: var(--primary); margin-bottom: 0.5rem;">📋 Bulk Question Import Format:</h4>
                <ul style="color: var(--text-secondary); margin: 0; padding-left: 1.5rem;">
                    <li><strong>File format:</strong> CSV with questions and answers</li>
                    <li><strong>Required columns:</strong> Title, Description, question_type, Answer</li>
                    <li><strong>Question types:</strong> text, multiple_choice, true_false</li>
                    <li><strong>Multiple choice format:</strong> A:Option A|B:Option B|C:Option C|D:Option D</li>
                    <li><strong>Download template:</strong> <a href="{% url 'admin_panel:admin_download_csv_template' %}?type=bulk" target="_blank" style="color: var(--primary);">Bulk Questions Template</a></li>
                </ul>
            </div>

            <form id="bulkImportForm" method="post" action="{% url 'admin_panel:admin_bulk_import_scenarios' %}" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-group">
                    <label class="form-label">🎯 Select Test Version to Import Questions Into *</label>
                    <select name="version_id" class="form-input" required>
                        <option value="">Choose a test version...</option>
                        {% for version in versions %}
                        <option value="{{ version.id }}">{{ version.name }} ({{ version.levels.count }} questions)</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">📁 Select Questions CSV File *</label>
                    <input type="file" name="bulk_csv_file" accept=".csv" class="form-input" required style="padding: 0.75rem;">
                    <small style="color: var(--text-muted); font-size: 0.8rem; margin-top: 0.5rem; display: block;">
                        Only .csv files are accepted. Maximum file size: 10MB
                    </small>
                </div>

                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary" style="flex: 1;">
                        📥 Import Questions
                    </button>
                    <button type="button" onclick="closeBulkImportModal()" class="btn btn-secondary">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function toggleVersionStatus(versionId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this version?`)) {
        // Implementation for toggling version status
        console.log(`${action} version ${versionId}`);
        // Add AJAX call here
    }
}

function duplicateVersion(versionId) {
    if (confirm('Create a copy of this version?')) {
        // Implementation for duplicating version
        console.log(`Duplicate version ${versionId}`);
        // Add AJAX call here
    }
}

function showBulkImportModal() {
    document.getElementById('bulkImportForm').reset();
    document.getElementById('bulkImportModal').style.display = 'flex';
}

function closeBulkImportModal() {
    document.getElementById('bulkImportModal').style.display = 'none';
}

function exportAllVersions() {
    if (confirm('Export all scenarios to CSV files?')) {
        window.location.href = '{% url "admin_panel:admin_export_all_scenarios" %}';
    }
}
</script>
{% endblock %}
